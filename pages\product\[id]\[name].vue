<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />
  <div class="product-detail q-pt-lg">
    <!-- 主体布局 -->
    <div class="row q-col-gutter-md">
      <!-- 左侧：商品图片展示区 -->
      <div class="col-12 col-md-6 col-lg-5">
        <!-- 大图轮播 -->
        <div class="carousel-container" :class="{ 'mobile-carousel-container': isMobile }">
          <q-carousel
            v-model="currentSlide"
            transition-prev="slide-right"
            transition-next="slide-left"
            animated
            :arrows="!isMobile"
            swipeable
            :height="isMobile ? 'auto' : '500px'"
            class="rounded-borders product-main-image"
            :class="{ 'mobile-carousel': isMobile }">
            <q-carousel-slide v-for="(image, index) in product.images" :key="index" :name="index" class="column items-center justify-center q-pa-sm" :class="{ 'mobile-carousel-slide': isMobile }">
              <q-img :src="image.src" :alt="image.alt" class="full-width" :fit="isMobile ? 'cover' : 'contain'" :class="{ 'mobile-carousel-img': isMobile }" />
            </q-carousel-slide>
          </q-carousel>

          <!-- 移动端轮播按钮 -->
          <template v-if="isMobile">
            <q-btn round flat dense color="white" icon="chevron_left" class="mobile-carousel-nav mobile-carousel-nav-left" @click="prevSlide" />
            <q-btn round flat dense color="white" icon="chevron_right" class="mobile-carousel-nav mobile-carousel-nav-right" @click="nextSlide" />
          </template>
        </div>

        <!-- 小图预览区域 - 桌面端和平板端 -->
        <div class="thumbnail-carousel q-mt-sm gt-xs">
          <q-carousel v-model="thumbnailSlide" control-color="primary" arrows swipeable animated height="110px" class="bg-grey-1">
            <!-- 每组5个小图 -->
            <q-carousel-slide v-for="(group, index) in groupedThumbnails" :key="index" :name="index" class="row no-wrap q-px-md" :class="{ 'justify-start': index === groupedThumbnails.length - 1 }">
              <q-img
                v-for="(image, idx) in group"
                :key="idx"
                :src="image.src"
                :alt="image.alt"
                class="thumbnail-item rounded-borders"
                :class="{ active: currentSlide === index * 5 + idx }"
                @click="currentSlide = index * 5 + idx" />
            </q-carousel-slide>
          </q-carousel>
        </div>

        <!-- 小图预览区域 - 移动端 -->
        <div class="thumbnail-carousel-mobile q-mt-sm lt-sm">
          <div class="row justify-center q-gutter-sm">
            <q-img
              v-for="(image, index) in product.images.slice(0, 5)"
              :key="index"
              :src="image.src"
              :alt="image.alt"
              class="thumbnail-item-mobile rounded-borders"
              :class="{ active: currentSlide === index }"
              @click="currentSlide = index" />
          </div>
        </div>
      </div>

      <!-- 右侧：商品信息 -->
      <div class="col-12 col-md-6 col-lg-7 q-pa-md">
        <h1 class="product-title q-mb-md">{{ product.title }}</h1>

        <!-- 店铺信息 -->
        <div class="row justify-between q-mt-sm">
          <div>
            <q-icon name="store" color="primary" size="20px" />
            <span class="q-ml-sm">{{ product.shopName }}</span>
          </div>
          <a v-if="product.type === 1" :href="product.sourceLink" target="_blank" class="text-primary">{{ $t('products.detail.productLink') }}</a>
        </div>

        <!-- 价格行 -->
        <div class="price-section q-mt-sm q-py-sm rounded-borders">
          <div class="row items-center q-px-md">
            <div class="col-12 col-sm-2 q-pr-md text-center text-sm-right label-text">{{ $t('products.detail.price') }}</div>
            <div class="col-12 col-sm-10 row items-center justify-center justify-sm-start q-mt-xs q-mt-sm-none" style="min-height: 32px">
              <span class="text-red text-bold text-h5" v-html="formatAmount(selectedSku.price || product.price, { allowWrap: false, useHtml: false })"></span>
              <q-btn v-if="!showPriceInput" flat dense icon="edit" color="primary" class="q-ml-sm q-px-xs" @click="showPriceInput = true" />
              <!-- 输入框和按钮 -->
              <div v-if="showPriceInput" class="row items-center q-mt-xs q-mt-sm-none">
                <q-input v-model="formattedCartPrice" dense outlined type="number" class="q-ml-sm q-pa-none" style="width: 80px" />
                <q-btn color="primary" dense flat :label="$t('products.detail.save')" class="q-ml-xs" @click="savePrice" />
                <q-btn color="grey" dense flat :label="$t('products.detail.cancel')" class="q-ml-xs" @click="cancelPrice" />
              </div>
              <!-- 问号提示 -->
              <q-icon name="help_outline" size="16px" class="q-ml-xs cursor-pointer text-primary">
                <q-tooltip anchor="top middle" self="bottom middle" :offset="[10, 10]">
                  <div>{{ $t('products.detail.priceEditTip') }}</div>
                </q-tooltip>
              </q-icon>
            </div>
          </div>
        </div>

        <!-- 商品信息和规格 -->
        <div class="product-info-container q-mt-md">
          <!-- 基本信息行 -->
          <div class="row q-col-gutter-sm mobile-center">
            <div class="col-6 col-sm-4">
              <div class="info-chip">
                <q-icon name="inventory_2" size="xs" class="q-mr-xs" />
                <span class="label-text q-mr-xs">{{ $t('products.detail.stock') }}:</span>
                <span class="value-text">{{ selectedSku.stock || product.stock }}</span>
              </div>
            </div>
            <div class="col-6 col-sm-4">
              <div class="info-chip">
                <q-icon name="local_shipping" size="xs" class="q-mr-xs" />
                <span class="label-text q-mr-xs">{{ $t('products.detail.freight') }}:</span>
                <span class="value-text">{{ (product.freight / 100).toFixed(2) }} RMB</span>
              </div>
            </div>
          </div>

          <!-- 商品规格行 -->
          <div class="specs-section q-mt-md">
            <!-- 使用紧凑布局 -->
            <div v-for="property in product.propertyList" :key="property.id" class="spec-item q-mb-sm">
              <div class="row no-wrap items-center q-mb-xs mobile-center">
                <div class="spec-label">{{ property.name }}:</div>
              </div>
              <div class="spec-values">
                <div class="attribute-box row q-gutter-xs mobile-center">
                  <q-btn
                    v-for="(value, index) in property.values"
                    :key="index"
                    :label="value.name"
                    :color="chooseState[property.id] === value.id ? 'primary' : 'grey-3'"
                    :text-color="chooseState[property.id] === value.id ? 'white' : 'black'"
                    unelevated
                    dense
                    :disable="value.disabled"
                    class="attribute-btn"
                    :class="{ disable: value.disabled, 'selected-attribute': chooseState[property.id] === value.id }"
                    @click="onSelectSku(property.id, value.id)" />
                </div>
              </div>
            </div>

            <!-- 购买数量 -->
            <div class="spec-item q-mb-sm">
              <div class="row no-wrap items-center q-mb-xs mobile-center">
                <div class="spec-label">{{ $t('products.detail.quantity') }}:</div>
              </div>
              <div class="quantity-selector mobile-center">
                <q-btn flat round dense color="primary" icon="remove" @click="decreaseQuantity" :disable="quantity <= 1" />
                <q-input
                  v-model.number="quantity"
                  dense
                  outlined
                  input-style="text-align: center"
                  class="quantity-input"
                  maxlength="4"
                  @update:model-value="checkQuantity"
                  oninput="value=value.replace(/[^0-9.]/g,'')" />
                <q-btn flat round dense color="primary" icon="add" @click="increaseQuantity" />
              </div>
            </div>

            <!-- 备注 -->
            <div class="spec-item q-mb-sm">
              <div class="row no-wrap items-center q-mb-xs mobile-center">
                <div class="spec-label">{{ $t('products.detail.memo') }}:</div>
              </div>
              <div class="memo-container">
                <q-input v-model="memo" outlined dense type="textarea" maxlength="1024" class="memo-input" rows="3" />
              </div>
            </div>
          </div>
        </div>

        <!-- 按钮组 -->
        <div class="action-buttons q-mt-md">
          <div class="row q-col-gutter-sm">
            <div class="col-12 col-sm-2 gt-xs"></div>
            <div class="col-12 col-sm-10">
              <div class="row q-col-gutter-sm justify-center justify-sm-start">
                <div class="col-6 col-sm-auto">
                  <q-btn color="primary" class="full-width action-btn cart-btn" @click="addToCart">
                    <q-icon name="shopping_cart" class="q-mr-xs" size="xs" />
                    {{ $t('products.detail.addToCart') }}
                  </q-btn>
                </div>
                <div class="col-6 col-sm-auto">
                  <q-btn color="deep-orange" class="full-width action-btn buy-btn" @click="buyNow">
                    <q-icon name="payments" class="q-mr-xs" size="xs" />
                    {{ $t('products.detail.buyNow') }}
                  </q-btn>
                </div>
                <div class="col-12 col-sm-auto">
                  <q-btn color="grey-7" flat class="full-width action-btn wishlist-btn q-mt-xs q-mt-sm-none" @click="toggleWishlist">
                    <q-icon name="favorite" color="pink" class="q-mr-xs" size="xs" />
                    {{ $t('products.detail.addToWishlist') }}
                  </q-btn>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- TAB 切换区域 -->
    <div class="product-tabs q-mt-xl">
      <q-tabs v-model="tab" align="left" class="text-primary" :breakpoint="0" dense :mobile-arrows="isMobile">
        <q-tab name="description" :label="$t('products.detail.description')" />
        <q-tab name="purchaseNotes" :label="$t('products.detail.purchaseNotes')" />
        <q-tab name="afterSale" :label="$t('products.detail.afterSale')" />
      </q-tabs>

      <q-separator />

      <q-tab-panels v-model="tab" animated class="bg-white">
        <q-tab-panel name="description">
          <div class="text-body1 product-description">
            <div v-html="product.description"></div>
          </div>
        </q-tab-panel>

        <q-tab-panel name="purchaseNotes">
          <div class="text-body1">
            <p>{{ $t('products.detail.purchaseNotesContent') }}</p>
          </div>
        </q-tab-panel>

        <q-tab-panel name="afterSale">
          <div class="text-body1">
            <p>{{ $t('products.detail.afterSaleContent') }}</p>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useCartStore } from '~~/store/cart';
import { useWishlistStore } from '~/store/wishlist';
import ProductApi from '~/composables/productApi';
import { useI18n } from 'vue-i18n';
import { useResponsive } from '~/composables/useResponsive';
import { useCurrency } from '~/composables/useCurrency';

const cartStore = useCartStore();
const wishlistStore = useWishlistStore();
const router = useRouter();
const route = useRoute(); // 获取路由信息
const productId = ref(route.params.id); // 获取路由中的商品 ID
const slug = route.params.name;

const { formatAmount } = useCurrency();

// 响应式布局
const { isMobile } = useResponsive();

// 轮播控制函数 - 用于移动端自定义导航按钮
const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  } else {
    currentSlide.value = product.images.length - 1;
  }
};

const nextSlide = () => {
  if (currentSlide.value < product.images.length - 1) {
    currentSlide.value++;
  } else {
    currentSlide.value = 0;
  }
};

const { t } = useI18n();
const breadcrumbs = [{ label: t('products.detail.productDetail'), to: `/product/${productId.value}/${slug}` }];

let product = reactive(null); // 存储商品详情
// 选中的状态：以属性 ID 作为 key
const chooseState = reactive({});
// 选中的 SKU
const selectedSku = ref({});

//购买数量
const quantity = ref(1);
//备注
const memo = ref('');

// 获取商品详情

const { code, data } = await ProductApi.getSpuDetail(productId.value);
if (code === 0) {
  // 如果 URL 中的 slug 不匹配，则进行 301 重定向
  const correctSlug = generateSlug(data.name);
  if (slug !== correctSlug) {
    router.replace(`/product/${productId.value}/${correctSlug}`);
  }
  product = ProductApi.convertBackendProductToTemplate(data);
}

// console.log('转换后的产品信息:', product);

// SKU 列表
const skuList = computed(() => {
  const skus = product.skus;
  for (const sku of skus) {
    sku.value_id_array = sku.properties.map((item) => item.valueId);
  }
  return skus;
});

const cartPrice = ref(product.cartPrice);
const showPriceInput = ref(false);

// 计算属性，用于显示和更新
const formattedCartPrice = computed({
  get() {
    // 显示为元
    return (cartPrice.value / 100).toFixed(2);
  },
  set(newValue) {
    // 转回分
    cartPrice.value = Math.round(parseFloat(newValue) * 100);
  },
});

const savePrice = () => {
  // product.price = cartPrice.value;
  console.log('selectedSku.value.length:', selectedSku.value.length);

  selectedSku.value.price = cartPrice.value;

  // if (!Object.keys(selectedSku.value).length === 0) {
  //   console.log('1');
  //   selectedSku.value.price = cartPrice.value * 100;
  // } else {
  //   // console.log('保存价格', cartPrice.value);
  //   console.log('2');
  //   product.price = cartPrice.value * 100;
  // }
  showPriceInput.value = false;
};

const cancelPrice = () => {
  cartPrice.value = product.price;
  showPriceInput.value = false;
};

// 当前轮播图的索引
const currentSlide = ref(0);

// 当前小图轮播的索引
const thumbnailSlide = ref(0);

// 每5个小图分组
const groupedThumbnails = computed(() => {
  const groups = [];
  for (let i = 0; i < product.images.length; i += 5) {
    groups.push(product.images.slice(i, i + 5));
  }
  return groups;
});

// 减少数量
const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value -= 1;
  }
};

// 增加数量
const increaseQuantity = () => {
  const stock = selectedSku.value.stock || product.stock;
  if (quantity.value >= stock) return;
  quantity.value += 1;
};

// 检查输入数量是否合法
const checkQuantity = () => {
  const stock = selectedSku.value.stock || product.stock;
  if (quantity.value > stock) {
    quantity.value = stock; // 超过库存自动修正为库存数量
  } else if (quantity.value < 1 || !quantity.value) {
    quantity.value = 1; // 小于 1 修正为 1
  }
};

// TAB 切换
const tab = ref('description');

// 选择规格
const onSelectSku = (propertyId, valueId) => {
  // 判断当前值是否已选中，如果选中则取消
  if (chooseState[propertyId] === valueId) {
    // 使用Vue的响应式API删除属性
    chooseState[propertyId] = undefined;
  } else {
    chooseState[propertyId] = valueId;
  }
  // 重新计算禁用状态和选中的 SKU
  updateDisabledStates();
  updateSelectedSku();
};

// 更新禁用状态
const updateDisabledStates = () => {
  // const selectedValues = Object.values(chooseState);
  product.propertyList.forEach((property) => {
    property.values.forEach((value) => {
      // 构建一个临时选择状态，将当前属性值替换进去
      const tempSelected = { ...chooseState, [property.id]: value.id };
      // 判断当前组合是否存在于 SKU 列表，且有库存
      const matchedSku = skuList.value.find((sku) => sku.stock > 0 && Object.values(tempSelected).every((id) => sku.value_id_array.includes(id)));
      value.disabled = !matchedSku; // 不匹配则禁用
    });
  });
};

// 更新选中的 SKU
const updateSelectedSku = () => {
  const selectedValues = Object.values(chooseState);
  // 如果没有完全选中所有属性，清空选中的 SKU
  if (selectedValues.length < product.propertyList.length) {
    selectedSku.value = {}; // 清空 selectedSku
    return;
  }
  // 查找符合当前属性组合的 SKU
  selectedSku.value = skuList.value.find((sku) => sku.stock > 0 && selectedValues.every((id) => sku.value_id_array.includes(id))) || {};
  //切换到选中sku的图片
  const imageItems = product.images.filter((item) => item.variant_id === selectedSku.value.id);
  if (imageItems && imageItems.length > 0 && imageItems[0].image_id !== undefined) {
    currentSlide.value = imageItems[0].image_id;
  }
  //纠正购买数量要小于库存数量
  if (selectedSku.value.stock < quantity.value) {
    quantity.value = selectedSku.value.stock;
  }
  //金额更新为所选金额
  cartPrice.value = selectedSku.value.price;
};

const addToCart = async () => {
  console.log('addToCart');
  if (Object.keys(selectedSku.value).length === 0) {
    useNuxtApp().$showNotify({ msg: t('products.detail.selectSpec'), type: 'negative' });
    return;
  }
  if (selectedSku.value.stock <= 0 || selectedSku.value.stock < quantity.value) {
    useNuxtApp().$showNotify({ msg: t('products.detail.insufficientStock'), type: 'negative' });
    return;
  }

  const cartItem = {
    id: 0,
    count: quantity.value || 1,
    selected: true,
    storeName: product.shopName || 'Shop Name',
    price: cartPrice.value || selectedSku.value.price || product.price,
    memo: memo.value || '',
    spu: {
      id: product.id,
      name: product.title,
      picUrl: product.images[0].src,
      categoryId: product.categoryId,
      freight: product.freight,
    },
    sku: {
      id: selectedSku.value.id,
      picUrl: selectedSku.value.picUrl || product.images[0].src || '',
      price: selectedSku.value.price,
      stock: selectedSku.value.stock,
      properties: selectedSku.value.properties,
    },
  };
  console.log('addToCart2', cartItem);
  try {
    // 调用 API
    await cartStore.add(cartItem);
    useNuxtApp().$showNotify({ msg: t('products.detail.addedToCart') });
    // 跳转到购物车页面
    router.push('/cart');
  } catch (error) {
    console.error('Failed to add to cart:', error);
    useNuxtApp().$showNotify({ msg: t('products.detail.addToCartFailed'), type: 'negative' });
  }
};

const buyNow = () => {
  console.log('buyNow');
};

// 切换心愿单状态
const toggleWishlist = (product) => {
  if (isFavorited.value) {
    wishlistStore.removeFromWishlist(product.id);
  } else {
    const productTemp = {
      id: 0,
      spu: {
        categoryId: product.category,
        id: product.id,
        name: product.title,
        picUrl: product.images[0].src,
        price: product.price,
      },
    };
    wishlistStore.addToWishlist(productTemp);
    useNuxtApp().$showNotify({ msg: t('products.detail.addedToWishlist'), type: 'success' });
  }
};
</script>
<style lang="scss" scoped>
.product-detail {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  .product-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 1.3;
    color: #333;
    margin-top: 0;
  }

  .carousel-container {
    position: relative;
    border: 1px solid #eee;
    background-color: #fff;

    &.mobile-carousel-container {
      width: 100%;
      padding-top: 100%; /* 创建1:1的宽高比（方形） */
      position: relative;
      overflow: hidden;
      border-radius: 4px;
    }

    .mobile-carousel {
      position: absolute !important;
      top: 0;
      left: 0;
      width: 100%;
      height: 100% !important;

      .mobile-carousel-slide {
        padding: 0 !important;
      }

      .mobile-carousel-img {
        height: 100%;
        width: 100%;
      }
    }

    .mobile-carousel-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      background: rgba(0, 0, 0, 0.3);
      color: white;
      width: 36px;
      height: 36px;

      &-left {
        left: 10px;
      }

      &-right {
        right: 10px;
      }
    }
  }

  .thumbnail-carousel {
    .thumbnail-item {
      width: 80px;
      height: 80px;
      margin-right: 5px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      object-fit: contain;
      background-color: #fff;

      &.active {
        border-color: #1976d2; /* 选中高亮边框 */
        transform: scale(1.05);
      }

      &:hover {
        border-color: #64b5f6;
      }
    }
  }

  .thumbnail-carousel-mobile {
    .thumbnail-item-mobile {
      width: 60px;
      height: 60px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      object-fit: contain;
      background-color: #fff;

      &.active {
        border-color: #1976d2; /* 选中高亮边框 */
      }
    }
  }

  .price-section {
    padding: 8px 0;
    background: linear-gradient(to right, #f9f9f9, #f0f0f0);
    border-left: 3px solid #1976d2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .text-h5 {
      font-weight: 700;
      letter-spacing: -0.3px;
      font-size: 1.5rem;
    }
  }

  .product-info-container {
    .info-chip {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 6px 8px;
      font-size: 14px;

      .label-text {
        color: #555;
        font-weight: 500;
      }

      .value-text {
        color: #333;
        font-weight: 600;
      }
    }
  }

  .specs-section {
    .label-text {
      color: #555;
      font-weight: 500;
      line-height: 1.4;
      font-size: 14px;
    }

    .spec-item {
      position: relative;
      padding-bottom: 10px;
      margin-bottom: 10px;
      border-bottom: 1px dashed #eee;

      &:last-child {
        border-bottom: none;
      }
    }

    .spec-label {
      color: #333;
      font-weight: 600;
      font-size: 15px;
      min-width: 70px;
    }
  }

  .attribute-box {
    margin: 3px 0;
    flex-wrap: wrap;

    .attribute-btn {
      margin: 3px 3px 3px 0;
      min-width: 70px;
      height: 32px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;

      &.selected-attribute {
        transform: scale(1.03);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
      }

      &:hover:not(.disable) {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      &.disable {
        background: #f5f5f5 !important;
        border: 1px dashed #bdbdbd;
        opacity: 0.7;
      }
    }
  }

  .quantity-selector {
    display: flex;
    align-items: center;

    .q-btn {
      border-radius: 50%;
      background-color: rgba(25, 118, 210, 0.1);
      width: 32px;
      height: 32px;

      &:hover {
        background-color: rgba(25, 118, 210, 0.2);
      }
    }

    .quantity-input {
      width: 65px;
      margin: 0 8px;

      :deep(.q-field__native) {
        text-align: center;
        font-weight: bold;
      }

      :deep(.q-field__control) {
        border-radius: 4px;
        height: 32px;
      }
    }
  }

  .mobile-center {
    @media (max-width: 1023px) {
      justify-content: center;
      text-align: center;
    }

    @media (min-width: 1024px) {
      justify-content: flex-start;
      text-align: left;
    }
  }

  .memo-container {
    display: flex;

    @media (max-width: 1023px) {
      justify-content: center;
    }

    @media (min-width: 1024px) {
      justify-content: flex-start;
    }
  }

  .memo-input {
    width: 100%;
    max-width: 100%;

    @media (max-width: 599px) {
      max-width: 90%;
    }

    :deep(.q-field__control) {
      border-radius: 4px;
    }
  }

  .spec-label {
    @media (max-width: 1023px) {
      text-align: center;
      margin: 0 auto;
    }

    @media (min-width: 1024px) {
      text-align: left;
      margin: 0;
    }
  }

  .action-buttons {
    margin-top: 20px;

    .action-btn {
      min-width: 130px;
      border-radius: 4px;
      font-weight: 500;
      letter-spacing: 0.3px;
      font-size: 14px;
      height: 36px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }

      &.cart-btn {
        background: linear-gradient(135deg, #1976d2, #0d47a1);
      }

      &.buy-btn {
        background: linear-gradient(135deg, #ff5722, #e64a19);
      }

      &.wishlist-btn {
        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }

  .product-tabs {
    margin-top: 50px;

    .product-description {
      padding: 20px 0;

      :deep(img) {
        max-width: 100%;
        height: auto;
      }
    }
  }
}

// 响应式样式
@media (max-width: 1023px) {
  .product-detail {
    .product-title {
      font-size: 18px;
    }

    .action-buttons {
      .q-btn {
        min-width: 120px;
      }
    }
  }
}

@media (max-width: 599px) {
  .product-detail {
    padding: 0 10px;

    .product-title {
      font-size: 16px;
      margin-top: 10px;
      text-align: center;
    }

    .price-section {
      padding: 8px 0;
      margin: 0 -10px;
      border-radius: 0;
      border-left: none;
      border-top: 3px solid #1976d2;
      background: linear-gradient(to bottom, #f9f9f9, #f0f0f0);

      .text-h5 {
        font-size: 1.4rem;
      }
    }

    .product-info-container {
      .info-chip {
        padding: 4px 8px;
        font-size: 13px;
      }
    }

    .specs-section {
      .spec-item {
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
        margin-bottom: 8px;
      }

      .spec-label {
        font-size: 14px;
        color: #1976d2;
      }

      .attribute-box {
        .attribute-btn {
          min-width: auto;
          padding: 0 12px;
          height: 30px;
          margin: 3px;
          border-radius: 4px;
          font-size: 13px;
        }
      }
    }

    .quantity-selector {
      display: flex;
      justify-content: center;

      .q-btn {
        width: 30px;
        height: 30px;
      }

      .quantity-input {
        width: 50px;

        :deep(.q-field__control) {
          height: 30px;
        }
      }
    }

    .action-buttons {
      margin-top: 15px;

      .q-btn {
        width: 100%;
        margin-bottom: 8px;
        border-radius: 6px;
        height: 38px;
        font-size: 14px;
      }
    }

    .product-tabs {
      margin-top: 20px;
    }
  }
}
</style>
