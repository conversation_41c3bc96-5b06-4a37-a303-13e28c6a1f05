<template>
  <Header />
  <div class="activities-page">
    <div class="activities-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">活动中心</h1>
        <div class="page-subtitle">参与精彩活动，享受独家优惠</div>
      </div>
      
      <!-- 活动筛选 -->
      <div class="filter-section q-mb-lg">
        <div class="row items-center justify-between">
          <div class="filter-tabs">
            <q-tabs
              v-model="filter.status"
              class="text-primary"
              active-color="primary"
              indicator-color="primary"
              narrow-indicator
              dense
            >
              <q-tab name="all" label="全部活动" />
              <q-tab name="ongoing" label="进行中" />
              <q-tab name="upcoming" label="即将开始" />
              <q-tab name="ended" label="已结束" />
            </q-tabs>
          </div>
          <div class="filter-sort">
            <q-select
              v-model="filter.sort"
              :options="sortOptions"
              outlined
              dense
              options-dense
              emit-value
              map-options
              style="width: 150px"
            />
          </div>
        </div>
      </div>
      
      <!-- 热门活动轮播 -->
      <div v-if="hotActivities.length > 0" class="hot-activities q-mb-xl">
        <h2 class="section-title">热门活动</h2>
        <q-carousel
          v-model="hotSlide"
          animated
          arrows
          navigation
          infinite
          :autoplay="3000"
          height="400px"
          class="rounded-borders"
        >
          <q-carousel-slide v-for="activity in hotActivities" :key="activity.id" :name="activity.id">
            <div class="row full-height">
              <div class="col-12 col-md-7 hot-activity-image-container">
                <q-img :src="activity.coverImage" class="hot-activity-image" />
                <div class="activity-status-badge" :class="getStatusClass(activity)">
                  {{ getStatusText(activity) }}
                </div>
              </div>
              <div class="col-12 col-md-5 hot-activity-content q-pa-md">
                <h3 class="hot-activity-title">{{ activity.title }}</h3>
                <div class="hot-activity-time q-mb-md">
                  <q-icon name="event" size="sm" class="q-mr-xs" />
                  {{ formatDateRange(activity.startDate, activity.endDate) }}
                </div>
                <p class="hot-activity-description">{{ activity.description }}</p>
                <div class="hot-activity-footer">
                  <q-btn color="primary" :to="`/activities/${activity.id}`" label="查看详情" />
                  <div v-if="isOngoing(activity)" class="countdown">
                    <span class="countdown-label">距结束还剩：</span>
                    <span class="countdown-value">{{ getCountdown(activity.endDate) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </q-carousel-slide>
        </q-carousel>
      </div>
      
      <!-- 活动列表 -->
      <div class="activities-list">
        <h2 class="section-title">{{ getFilterTitle() }}</h2>
        
        <div v-if="displayedActivities.length > 0" class="row q-col-gutter-md">
          <div v-for="activity in displayedActivities" :key="activity.id" class="col-12 col-sm-6 col-md-4">
            <q-card class="activity-card">
              <div class="activity-image-container">
                <q-img :src="activity.coverImage" :ratio="16/9" class="activity-image" />
                <div class="activity-status-badge" :class="getStatusClass(activity)">
                  {{ getStatusText(activity) }}
                </div>
              </div>
              <q-card-section>
                <div class="row items-center no-wrap">
                  <div class="col">
                    <div class="text-h6 activity-title">{{ activity.title }}</div>
                  </div>
                  <div class="col-auto">
                    <q-btn flat round color="grey" icon="share">
                      <q-tooltip>分享活动</q-tooltip>
                    </q-btn>
                  </div>
                </div>
                
                <div class="activity-time q-mt-sm">
                  <q-icon name="event" size="xs" class="q-mr-xs" />
                  {{ formatDateRange(activity.startDate, activity.endDate) }}
                </div>
                
                <p class="activity-description q-mt-sm">{{ activity.description }}</p>
                
                <div class="activity-tags q-mt-sm">
                  <q-chip
                    v-for="tag in activity.tags"
                    :key="tag"
                    size="sm"
                    outline
                    color="primary"
                    class="q-mr-xs"
                  >
                    {{ tag }}
                  </q-chip>
                </div>
              </q-card-section>
              
              <q-card-actions align="right">
                <div v-if="isOngoing(activity)" class="countdown q-mr-auto">
                  <span class="countdown-label">距结束：</span>
                  <span class="countdown-value">{{ getCountdown(activity.endDate) }}</span>
                </div>
                <q-btn flat color="primary" :to="`/activities/${activity.id}`">
                  查看详情
                </q-btn>
              </q-card-actions>
            </q-card>
          </div>
        </div>
        
        <div v-else class="no-activities">
          <q-icon name="event_busy" size="4rem" color="grey-5" />
          <p class="text-grey-7 q-mt-md">暂无符合条件的活动</p>
        </div>
        
        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination-container q-mt-lg">
          <q-pagination
            v-model="currentPage"
            :max="totalPages"
            :max-pages="5"
            boundary-numbers
            direction-links
            @update:model-value="handlePageChange"
            class="justify-center"
          />
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';

const router = useRouter();
const route = useRoute();

// 分页相关
const currentPage = ref(1);
const pageSize = ref(9);
const totalActivities = ref(0);

// 筛选和排序
const filter = ref({
  status: 'all',
  sort: 'latest'
});

const sortOptions = [
  { label: '最新发布', value: 'latest' },
  { label: '即将结束', value: 'ending_soon' },
  { label: '即将开始', value: 'starting_soon' },
  { label: '人气最高', value: 'popular' }
];

// 热门活动轮播
const hotSlide = ref(1);

// 从URL获取页码和筛选条件
onMounted(() => {
  if (route.query.page) {
    currentPage.value = parseInt(route.query.page) || 1;
  }
  if (route.query.status) {
    filter.value.status = route.query.status;
  }
  if (route.query.sort) {
    filter.value.sort = route.query.sort;
  }
  fetchActivities();
});

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalActivities.value / pageSize.value);
});

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
  updateRouteQuery();
  fetchActivities();
};

// 更新路由查询参数
const updateRouteQuery = () => {
  router.push({
    path: '/activities',
    query: {
      page: currentPage.value,
      status: filter.value.status,
      sort: filter.value.sort
    }
  });
};

// 监听筛选条件变化
watch(
  () => [filter.value.status, filter.value.sort],
  () => {
    currentPage.value = 1; // 重置页码
    updateRouteQuery();
    fetchActivities();
  }
);

// 模拟活动数据
const activities = ref([
  {
    id: 1,
    title: '夏日特惠：全场商品8折起',
    description: '炎炎夏日，清凉特惠！全场商品8折起，部分热门商品低至5折，还有限量夏日礼包等你来拿！',
    startDate: '2023-07-01',
    endDate: '2023-08-15',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    tags: ['限时折扣', '全场优惠'],
    isHot: true,
    participants: 1256,
    status: 'ended'
  },
  {
    id: 2,
    title: '开学季：学生专享优惠',
    description: '开学季来临，学生专享优惠！凭学生证购买指定商品享受额外9折优惠，还有机会赢取iPad等学习用品！',
    startDate: '2023-08-15',
    endDate: '2023-09-15',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    tags: ['学生专享', '数码产品'],
    isHot: true,
    participants: 876,
    status: 'ended'
  },
  {
    id: 3,
    title: '中秋国庆双节钜惠',
    description: '中秋国庆双节同庆，多重好礼等你拿！购物满300减50，满500减100，还有精美月饼礼盒赠送！',
    startDate: '2023-09-20',
    endDate: '2023-10-07',
    coverImage: 'https://cdn.quasar.dev/img/parallax2.jpg',
    tags: ['节日特惠', '满减活动'],
    isHot: true,
    participants: 2103,
    status: 'ended'
  },
  {
    id: 4,
    title: '双11全球购物节',
    description: '一年一度的购物盛宴！全球商品低至3折，限时闪购，抢先预售，更有百万优惠券等你来抢！',
    startDate: '2023-11-01',
    endDate: '2023-11-12',
    coverImage: 'https://cdn.quasar.dev/img/quasar.jpg',
    tags: ['双11特惠', '全球购物'],
    isHot: true,
    participants: 5689,
    status: 'ended'
  },
  {
    id: 5,
    title: '圣诞跨年欢乐购',
    description: '圣诞新年双重惊喜！精选圣诞礼物低至5折，跨年倒计时限时抢购，还有神秘圣诞礼盒等你来拆！',
    startDate: '2023-12-15',
    endDate: '2024-01-05',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    tags: ['节日特惠', '礼品专区'],
    isHot: false,
    participants: 3245,
    status: 'ended'
  },
  {
    id: 6,
    title: '春节年货节',
    description: '辞旧迎新，好礼不断！年货专区低至7折，满1000减150，还有新年限定礼盒和红包等你来拿！',
    startDate: '2024-01-15',
    endDate: '2024-02-10',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    tags: ['年货节', '满减活动'],
    isHot: true,
    participants: 4521,
    status: 'ended'
  },
  {
    id: 7,
    title: '情人节浪漫特惠',
    description: '爱在情人节，浪漫特惠！精选情侣礼物低至8折，购物满500送精美玫瑰礼盒，让爱更有仪式感！',
    startDate: '2024-02-07',
    endDate: '2024-02-14',
    coverImage: 'https://cdn.quasar.dev/img/parallax2.jpg',
    tags: ['情人节', '礼品专区'],
    isHot: false,
    participants: 2876,
    status: 'ended'
  },
  {
    id: 8,
    title: '春季焕新活动',
    description: '春暖花开，焕然一新！春季新品首发，限时9折，春季穿搭指南，让你的衣橱也迎来春天！',
    startDate: '2024-03-01',
    endDate: '2024-03-31',
    coverImage: 'https://cdn.quasar.dev/img/quasar.jpg',
    tags: ['春季特惠', '时尚穿搭'],
    isHot: false,
    participants: 1987,
    status: 'ended'
  },
  {
    id: 9,
    title: '五一劳动节特惠',
    description: '五一小长假，快乐购不停！全场商品低至7折，满300减50，更有五一专属礼包等你来拿！',
    startDate: '2024-04-27',
    endDate: '2024-05-05',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    tags: ['节日特惠', '满减活动'],
    isHot: false,
    participants: 3102,
    status: 'ended'
  },
  {
    id: 10,
    title: '618年中购物节',
    description: '年中大促，好物半价起！全场商品低至5折，每满300减50，抢先预售享额外9折，618购物狂欢等你来！',
    startDate: '2024-06-01',
    endDate: '2024-06-18',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    tags: ['618特惠', '全场优惠'],
    isHot: true,
    participants: 6789,
    status: 'ongoing'
  },
  {
    id: 11,
    title: '夏日清凉特惠',
    description: '夏日来临，清凉特惠！夏季新品首发，限时8.5折，还有夏日清凉礼包等你来拿！',
    startDate: '2024-07-01',
    endDate: '2024-08-15',
    coverImage: 'https://cdn.quasar.dev/img/parallax2.jpg',
    tags: ['夏季特惠', '新品首发'],
    isHot: false,
    participants: 0,
    status: 'upcoming'
  },
  {
    id: 12,
    title: '开学季学生专享',
    description: '新学期，新气象！学生专享优惠，数码产品低至8折，文具用品满100减30，还有机会赢取iPad！',
    startDate: '2024-08-15',
    endDate: '2024-09-15',
    coverImage: 'https://cdn.quasar.dev/img/quasar.jpg',
    tags: ['学生专享', '数码产品'],
    isHot: false,
    participants: 0,
    status: 'upcoming'
  }
]);

// 更新活动状态
const updateActivityStatus = () => {
  const today = new Date();
  
  activities.value.forEach(activity => {
    const startDate = new Date(activity.startDate);
    const endDate = new Date(activity.endDate);
    
    if (today < startDate) {
      activity.status = 'upcoming';
    } else if (today > endDate) {
      activity.status = 'ended';
    } else {
      activity.status = 'ongoing';
    }
  });
};

// 模拟API请求获取活动列表
const fetchActivities = () => {
  // 更新活动状态
  updateActivityStatus();
  
  // 根据筛选条件过滤活动
  let filteredActivities = [...activities.value];
  
  if (filter.value.status !== 'all') {
    filteredActivities = filteredActivities.filter(activity => activity.status === filter.value.status);
  }
  
  // 根据排序条件排序活动
  switch (filter.value.sort) {
    case 'latest':
      filteredActivities.sort((a, b) => new Date(b.startDate) - new Date(a.startDate));
      break;
    case 'ending_soon':
      filteredActivities.sort((a, b) => new Date(a.endDate) - new Date(b.endDate));
      break;
    case 'starting_soon':
      filteredActivities.sort((a, b) => new Date(a.startDate) - new Date(b.startDate));
      break;
    case 'popular':
      filteredActivities.sort((a, b) => b.participants - a.participants);
      break;
  }
  
  // 计算总活动数
  totalActivities.value = filteredActivities.length;
  
  // 在实际应用中，这里应该是API调用
  // const response = await fetch(`/api/activities?page=${currentPage.value}&pageSize=${pageSize.value}&status=${filter.value.status}&sort=${filter.value.sort}`);
  // const data = await response.json();
  // activities.value = data.items;
  // totalActivities.value = data.total;
};

// 热门活动
const hotActivities = computed(() => {
  return activities.value.filter(activity => activity.isHot && (activity.status === 'ongoing' || activity.status === 'upcoming')).slice(0, 5);
});

// 根据当前页码和页面大小计算要显示的活动
const displayedActivities = computed(() => {
  // 更新活动状态
  updateActivityStatus();
  
  // 根据筛选条件过滤活动
  let filteredActivities = [...activities.value];
  
  if (filter.value.status !== 'all') {
    filteredActivities = filteredActivities.filter(activity => activity.status === filter.value.status);
  }
  
  // 根据排序条件排序活动
  switch (filter.value.sort) {
    case 'latest':
      filteredActivities.sort((a, b) => new Date(b.startDate) - new Date(a.startDate));
      break;
    case 'ending_soon':
      filteredActivities.sort((a, b) => new Date(a.endDate) - new Date(b.endDate));
      break;
    case 'starting_soon':
      filteredActivities.sort((a, b) => new Date(a.startDate) - new Date(b.startDate));
      break;
    case 'popular':
      filteredActivities.sort((a, b) => b.participants - a.participants);
      break;
  }
  
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredActivities.slice(start, end);
});

// 格式化日期范围
const formatDateRange = (startDate, endDate) => {
  const start = date.formatDate(new Date(startDate), 'YYYY-MM-DD');
  const end = date.formatDate(new Date(endDate), 'YYYY-MM-DD');
  return `${start} 至 ${end}`;
};

// 获取活动状态文本
const getStatusText = (activity) => {
  switch (activity.status) {
    case 'upcoming':
      return '即将开始';
    case 'ongoing':
      return '进行中';
    case 'ended':
      return '已结束';
    default:
      return '';
  }
};

// 获取活动状态样式类
const getStatusClass = (activity) => {
  switch (activity.status) {
    case 'upcoming':
      return 'status-upcoming';
    case 'ongoing':
      return 'status-ongoing';
    case 'ended':
      return 'status-ended';
    default:
      return '';
  }
};

// 判断活动是否正在进行中
const isOngoing = (activity) => {
  return activity.status === 'ongoing';
};

// 获取倒计时
const getCountdown = (endDateStr) => {
  const now = new Date();
  const endDate = new Date(endDateStr);
  const diff = endDate - now;
  
  if (diff <= 0) {
    return '已结束';
  }
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  if (days > 0) {
    return `${days}天${hours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

// 获取筛选标题
const getFilterTitle = () => {
  switch (filter.value.status) {
    case 'ongoing':
      return '进行中的活动';
    case 'upcoming':
      return '即将开始的活动';
    case 'ended':
      return '已结束的活动';
    default:
      return '全部活动';
  }
};
</script>

<style lang="scss" scoped>
.activities-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.activities-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;
  
  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }
}

.filter-section {
  margin-bottom: 30px;
}

.hot-activities {
  margin-bottom: 40px;
}

.hot-activity-image-container {
  position: relative;
  height: 100%;
}

.hot-activity-image {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.hot-activity-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
}

.hot-activity-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.hot-activity-time {
  color: #666;
  font-size: 14px;
}

.hot-activity-description {
  flex-grow: 1;
  margin: 10px 0;
  color: #555;
  font-size: 16px;
  line-height: 1.5;
}

.hot-activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.activity-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.activity-image-container {
  position: relative;
}

.activity-status-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  
  &.status-upcoming {
    background-color: #ff9800;
  }
  
  &.status-ongoing {
    background-color: #4caf50;
  }
  
  &.status-ended {
    background-color: #9e9e9e;
  }
}

.activity-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-time {
  color: #666;
  font-size: 13px;
}

.activity-description {
  color: #555;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 0;
}

.countdown {
  font-size: 13px;
  color: #ff5722;
  
  .countdown-label {
    font-weight: normal;
  }
  
  .countdown-value {
    font-weight: bold;
  }
}

.no-activities {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px;
}

@media (max-width: 767px) {
  .page-header {
    margin-bottom: 30px;
    padding: 20px 0;
  }
  
  .page-title {
    font-size: 26px;
  }
  
  .page-subtitle {
    font-size: 16px;
  }
  
  .section-title {
    font-size: 22px;
    margin-bottom: 15px;
  }
  
  .hot-activity-title {
    font-size: 20px;
  }
  
  .hot-activity-description {
    font-size: 14px;
  }
  
  .activity-title {
    font-size: 16px;
  }
  
  .activity-description {
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
  
  .pagination-container {
    margin: 30px 0 15px;
  }
}
</style>
