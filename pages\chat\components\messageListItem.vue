<template>
  <div class="chat-box">
    <!--  消息渲染  -->
    <div class="message-item">
      <div class="message-time-container">
        <!-- 日期 -->
        <div v-if="message.contentType !== KeFuMessageContentTypeEnum.SYSTEM && showTime(message, messageIndex)" class="date-message">
          {{ formatTime(message.createTime) }}
        </div>
        <!-- 系统消息 -->
        <div v-if="message.contentType === KeFuMessageContentTypeEnum.SYSTEM" class="system-message">
          {{ message.content }}
        </div>
      </div>

      <!-- 消息体渲染客服消息和用户消息并左右展示  -->
      <div
        v-if="message.contentType !== KeFuMessageContentTypeEnum.SYSTEM"
        class="message-content"
        :class="{
          'admin-message': message.senderType === 2, // 客服发送的消息
          'user-message': message.senderType === 1, // 用户发送的消息
        }">
        <!-- 客服头像 -->
        <q-avatar v-if="message.senderType === 2" class="chat-avatar" size="35px">
          <img src="/images/avatar/admin.png" />
        </q-avatar>

        <!-- 内容 -->
        <div class="message-bubble" :class="{ 'admin-bubble': message.senderType === 2, 'user-bubble': message.senderType === 1 }">
          <!-- 文本消息 -->
          <template v-if="message.contentType === KeFuMessageContentTypeEnum.TEXT">
            <div v-html="replaceEmoji(message.content)"></div>
          </template>

          <!-- 图片消息 -->
          <template v-else-if="message.contentType === KeFuMessageContentTypeEnum.IMAGE">
            <q-img class="message-img" :src="message.content" spinner-color="primary" spinner-size="24px" @click="openImagePreview(message.content)" />
          </template>

          <!-- 商品消息 -->
          <template v-else-if="message.contentType === KeFuMessageContentTypeEnum.PRODUCT">
            <GoodsItem :goodsData="getMessageContent(message)" @click="navigateToProduct(getMessageContent(message))" />
          </template>

          <!-- 订单消息 -->
          <template v-else-if="message.contentType === KeFuMessageContentTypeEnum.ORDER">
            <OrderItem :orderData="getMessageContent(message)" @click="navigateToOrder(getMessageContent(message))" />
          </template>
        </div>

        <!-- 用户头像 -->
        <q-avatar v-if="message.senderType === 1" class="chat-avatar" size="35px">
          <img src="/images/avatar/user.png" />
        </q-avatar>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, unref } from 'vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn'; // 导入中文语言包
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { KeFuMessageContentTypeEnum } from '../util/constants';
import { emojiList } from '../util/emoji';
import GoodsItem from './goods.vue';
import OrderItem from './order.vue';

// 配置dayjs
dayjs.extend(relativeTime); // 启用相对时间插件
dayjs.locale('zh-cn'); // 设置语言为中文

const router = useRouter();
const $q = useQuasar();

const props = defineProps({
  // 消息
  message: {
    type: Object,
    default: () => ({}),
  },
  // 消息索引
  messageIndex: {
    type: Number,
    default: 0,
  },
  // 消息列表
  messageList: {
    type: Array,
    default: () => [],
  },
});

// 解析消息内容
const getMessageContent = computed(() => (item) => {
  try {
    return JSON.parse(item.content);
  } catch (error) {
    console.error('解析消息内容失败', error);
    return {};
  }
});

// 判断是否显示时间
const showTime = computed(() => (item, index) => {
  if (unref(props.messageList)[index + 1]) {
    const dateString = dayjs(unref(props.messageList)[index + 1].createTime).fromNow();
    return dateString !== dayjs(unref(item).createTime).fromNow();
  }
  return false;
});

// 格式化时间
function formatTime(dateStr) {
  if (!dateStr) return '';
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm');
}

// 处理表情
function replaceEmoji(data) {
  if (!data) return '';

  let newData = data;
  if (typeof newData !== 'object') {
    const reg = /\[(.+?)]/g; // [] 中括号
    const zhEmojiName = newData.match(reg);
    if (zhEmojiName) {
      zhEmojiName.forEach((item) => {
        const emojiFile = selEmojiFile(item);
        if (emojiFile) {
          newData = newData.replace(item, `<img class="chat-img" style="width: 24px;height: 24px;margin: 0 3px;" src="/images/emoji/${emojiFile}"/>`);
        }
      });
    }
  }
  return newData;
}

// 查找表情文件
function selEmojiFile(name) {
  for (const index in emojiList) {
    if (emojiList[index].name === name) {
      return emojiList[index].file;
    }
  }
  return false;
}

// 打开图片预览
function openImagePreview(imageUrl) {
  // 使用Quasar的图片预览功能
  $q.dialog({
    component: 'div',
    componentProps: {
      style: 'max-width: 90vw; max-height: 90vh;',
      innerHTML: `<img src="${imageUrl}" style="max-width: 100%; max-height: 90vh; object-fit: contain;">`,
    },
  }).onOk(() => {
    // 关闭预览
  });
}

// 导航到商品详情
function navigateToProduct(product) {
  if (product && product.id) {
    router.push(`/product/${product.id}`);
  }
}

// 导航到订单详情
function navigateToOrder(order) {
  if (order && order.id) {
    router.push(`/order/detail?id=${order.id}`);
  }
}
</script>

<style scoped lang="scss">
.chat-box {
  width: 100%;

  .message-item {
    margin-bottom: 20px;
    transition: all 0.3s ease;

    .message-time-container {
      display: flex;
      justify-content: center;
      margin-bottom: 12px;

      .date-message,
      .system-message {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 16px;
        background-color: rgba(255, 255, 255, 0.8);
        color: #666;
        font-size: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);
      }
    }

    .message-content {
      display: flex;
      align-items: flex-start;
      margin: 0 10px;

      &.admin-message {
        justify-content: flex-start;
      }

      &.user-message {
        justify-content: flex-end;
      }

      .chat-avatar {
        margin: 0 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border: 2px solid white;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }

      .message-bubble {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-break: break-word;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        position: relative;
        font-size: 14px;
        line-height: 1.5;

        // 用户消息气泡样式
        &.user-bubble {
          background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
          color: white;

          // 用户消息气泡尖角
          &::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 12px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 8px solid #1565c0;
          }
        }

        // 客服消息气泡样式
        &.admin-bubble {
          background: white;
          color: #333;
          border: 1px solid rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

          // 客服消息气泡尖角
          &::after {
            content: '';
            position: absolute;
            left: -8px;
            top: 12px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 8px solid white;
          }
        }

        .message-img {
          max-width: 200px;
          max-height: 200px;
          border-radius: 8px;
          cursor: pointer;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.03);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }
}

// 商品和订单样式
:deep(.goods-item),
:deep(.order-item) {
  max-width: 280px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 表情样式
:deep(.chat-img) {
  display: inline-block;
  vertical-align: middle;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.2);
  }
}
</style>
