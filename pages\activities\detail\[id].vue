<template>
  <Header />
  <div class="activity-detail-page">
    <div class="activity-container">
      <!-- 活动基本信息 -->
      <div v-if="activity" class="activity-content-wrapper">
        <!-- 活动标题和状态 -->
        <div class="activity-header">
          <div class="row items-center justify-between q-mb-md">
            <h1 class="activity-title">{{ activity.title }}</h1>
            <div class="activity-status-badge" :class="getStatusClass(activity)">
              {{ getStatusText(activity) }}
            </div>
          </div>

          <div class="activity-meta">
            <div class="meta-item">
              <q-icon name="event" size="sm" class="q-mr-xs" />
              <span>{{ formatDateRange(activity.startDate, activity.endDate) }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="people" size="sm" class="q-mr-xs" />
              <span>{{ activity.participants }} 人参与</span>
            </div>
            <div v-if="isOngoing(activity)" class="meta-item countdown">
              <q-icon name="timer" size="sm" class="q-mr-xs" />
              <span>距结束还剩：{{ getCountdown(activity.endDate) }}</span>
            </div>
            <div v-if="isUpcoming(activity)" class="meta-item countdown">
              <q-icon name="timer" size="sm" class="q-mr-xs" />
              <span>距开始还剩：{{ getCountdown(activity.startDate) }}</span>
            </div>
          </div>

          <div class="activity-tags q-mt-sm">
            <q-chip v-for="tag in activity.tags" :key="tag" outline color="primary" size="sm" class="q-mr-xs">
              {{ tag }}
            </q-chip>
          </div>
        </div>

        <!-- 活动封面图 -->
        <div class="activity-cover q-my-md">
          <q-img :src="activity.coverImage" :ratio="16 / 9" class="rounded-borders" />
        </div>

        <!-- 活动内容 -->
        <div class="activity-tabs">
          <q-tabs v-model="activeTab" class="text-primary" active-color="primary" indicator-color="primary" align="left" narrow-indicator>
            <q-tab name="details" label="活动详情" />
            <q-tab name="rules" label="活动规则" />
            <q-tab name="prizes" label="奖励说明" />
            <q-tab name="faq" label="常见问题" />
          </q-tabs>

          <q-separator />

          <q-tab-panels v-model="activeTab" animated>
            <q-tab-panel name="details">
              <div class="activity-details">
                <h2 class="section-title">活动详情</h2>
                <div v-html="activity.details"></div>

                <!-- 活动商品 -->
                <div v-if="activity.products && activity.products.length > 0" class="activity-products q-mt-xl">
                  <h3 class="subsection-title">活动商品</h3>
                  <div class="row q-col-gutter-md q-mt-md">
                    <div v-for="product in activity.products" :key="product.id" class="col-6 col-sm-4 col-md-3">
                      <q-card class="product-card">
                        <q-img :src="product.image" :ratio="1" />
                        <q-card-section>
                          <div class="product-title">{{ product.name }}</div>
                          <div class="product-price-row">
                            <div class="product-price">
                              <span class="current-price">${{ (product.activityPrice / 100).toFixed(2) }}</span>
                              <span class="original-price">${{ (product.originalPrice / 100).toFixed(2) }}</span>
                            </div>
                            <div class="discount-tag">{{ calculateDiscount(product.originalPrice, product.activityPrice) }}折</div>
                          </div>
                        </q-card-section>
                        <q-card-actions align="right">
                          <q-btn flat color="primary" :to="`/product/${product.id}`">查看详情</q-btn>
                        </q-card-actions>
                      </q-card>
                    </div>
                  </div>
                </div>
              </div>
            </q-tab-panel>

            <q-tab-panel name="rules">
              <div class="activity-rules">
                <h2 class="section-title">活动规则</h2>
                <div v-html="activity.rules"></div>
              </div>
            </q-tab-panel>

            <q-tab-panel name="prizes">
              <div class="activity-prizes">
                <h2 class="section-title">奖励说明</h2>
                <div v-html="activity.prizes"></div>
              </div>
            </q-tab-panel>

            <q-tab-panel name="faq">
              <div class="activity-faq">
                <h2 class="section-title">常见问题</h2>
                <q-list bordered separator>
                  <q-expansion-item v-for="(faq, index) in activity.faqs" :key="index" :label="faq.question" header-class="text-primary" expand-icon-class="text-primary">
                    <q-card>
                      <q-card-section>
                        {{ faq.answer }}
                      </q-card-section>
                    </q-card>
                  </q-expansion-item>
                </q-list>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </div>

        <!-- 活动按钮 -->
        <div class="activity-actions q-mt-lg">
          <div class="row justify-between items-center">
            <div class="social-share">
              <q-btn flat round color="primary" icon="share" class="q-mr-sm">
                <q-tooltip>分享活动</q-tooltip>
              </q-btn>
              <q-btn flat round :color="isLiked ? 'red' : 'grey'" icon="favorite" @click="toggleLike">
                <q-tooltip>{{ isLiked ? '取消收藏' : '收藏活动' }}</q-tooltip>
              </q-btn>
              <span class="q-ml-xs">{{ likeCount }}</span>
            </div>
            <div>
              <q-btn v-if="isOngoing(activity)" color="primary" label="立即参与" @click="participateActivity" />
              <q-btn v-if="isUpcoming(activity)" color="orange" label="预约提醒" @click="reserveActivity" />
              <q-btn v-if="isEnded(activity)" color="grey" label="活动已结束" disable />
              <q-btn flat color="primary" to="/activities" class="q-ml-sm"> 返回列表 </q-btn>
            </div>
          </div>
        </div>
      </div>

      <!-- 相关活动 -->
      <div v-if="relatedActivities.length > 0" class="related-activities q-mt-xl">
        <h2 class="section-title">相关活动</h2>
        <div class="row q-col-gutter-md q-mt-md">
          <div v-for="activity in relatedActivities" :key="activity.id" class="col-12 col-sm-6 col-md-4">
            <q-card class="activity-card">
              <div class="activity-image-container">
                <q-img :src="activity.coverImage" :ratio="16 / 9" class="activity-image" />
                <div class="activity-status-badge" :class="getStatusClass(activity)">
                  {{ getStatusText(activity) }}
                </div>
              </div>
              <q-card-section>
                <div class="text-h6 activity-title">{{ activity.title }}</div>
                <div class="activity-time q-mt-sm">
                  <q-icon name="event" size="xs" class="q-mr-xs" />
                  {{ formatDateRange(activity.startDate, activity.endDate) }}
                </div>
                <p class="activity-description q-mt-sm">{{ activity.description }}</p>
              </q-card-section>
              <q-card-actions align="right">
                <q-btn flat color="primary" :to="`/activities/${activity.id}`"> 查看详情 </q-btn>
              </q-card-actions>
            </q-card>
          </div>
        </div>
      </div>

      <!-- 加载中状态 -->
      <div v-else-if="loading" class="loading-state">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-state">
        <q-icon name="error_outline" color="negative" size="3em" />
        <div class="q-mt-md">活动不存在或已被删除</div>
        <q-btn color="primary" to="/activities" class="q-mt-md"> 返回活动列表 </q-btn>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { date, useQuasar } from 'quasar';

const $q = useQuasar();
const route = useRoute();
const activityId = computed(() => parseInt(route.params.id));

// 状态
const loading = ref(true);
const activity = ref(null);
const activeTab = ref('details');
const isLiked = ref(false);
const likeCount = ref(0);

// 获取活动详情
onMounted(async () => {
  try {
    loading.value = true;

    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 在实际应用中，这里应该是API调用
    // const response = await fetch(`/api/activities/${activityId.value}`);
    // const data = await response.json();
    // activity.value = data;

    // 使用模拟数据
    const foundActivity = activitiesData.find((item) => item.id === activityId.value);
    if (foundActivity) {
      activity.value = foundActivity;
      likeCount.value = Math.floor(Math.random() * 100) + 50; // 模拟点赞数
    }

    loading.value = false;
  } catch (error) {
    console.error('Failed to fetch activity details:', error);
    loading.value = false;
  }
});

// 相关活动（根据标签匹配）
const relatedActivities = computed(() => {
  if (!activity.value) return [];

  // 找出与当前活动标签相似的其他活动
  return activitiesData
    .filter((item) => item.id !== activityId.value) // 排除当前活动
    .filter((item) => {
      // 检查是否有共同的标签
      return item.tags.some((tag) => activity.value.tags.includes(tag));
    })
    .slice(0, 3); // 最多显示3个相关活动
});

// 格式化日期范围
const formatDateRange = (startDate, endDate) => {
  const start = date.formatDate(new Date(startDate), 'YYYY-MM-DD');
  const end = date.formatDate(new Date(endDate), 'YYYY-MM-DD');
  return `${start} 至 ${end}`;
};

// 获取活动状态文本
const getStatusText = (activity) => {
  switch (activity.status) {
    case 'upcoming':
      return '即将开始';
    case 'ongoing':
      return '进行中';
    case 'ended':
      return '已结束';
    default:
      return '';
  }
};

// 获取活动状态样式类
const getStatusClass = (activity) => {
  switch (activity.status) {
    case 'upcoming':
      return 'status-upcoming';
    case 'ongoing':
      return 'status-ongoing';
    case 'ended':
      return 'status-ended';
    default:
      return '';
  }
};

// 判断活动是否正在进行中
const isOngoing = (activity) => {
  return activity.status === 'ongoing';
};

// 判断活动是否即将开始
const isUpcoming = (activity) => {
  return activity.status === 'upcoming';
};

// 判断活动是否已结束
const isEnded = (activity) => {
  return activity.status === 'ended';
};

// 获取倒计时
const getCountdown = (dateStr) => {
  const now = new Date();
  const targetDate = new Date(dateStr);
  const diff = targetDate - now;

  if (diff <= 0) {
    return '已结束';
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}天${hours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

// 计算折扣
const calculateDiscount = (originalPrice, activityPrice) => {
  if (!originalPrice || !activityPrice) return '10';
  const discount = (activityPrice / originalPrice) * 10;
  return discount.toFixed(1);
};

// 点赞/收藏功能
const toggleLike = () => {
  isLiked.value = !isLiked.value;
  likeCount.value += isLiked.value ? 1 : -1;

  // 在实际应用中，这里应该调用API更新收藏状态
  // await fetch(`/api/activities/${activityId.value}/like`, {
  //   method: 'POST',
  //   body: JSON.stringify({ liked: isLiked.value }),
  //   headers: { 'Content-Type': 'application/json' }
  // });
};

// 参与活动
const participateActivity = () => {
  $q.notify({
    color: 'positive',
    message: '您已成功参与活动！',
    icon: 'check_circle',
    position: 'top',
    timeout: 2000,
  });
};

// 预约活动提醒
const reserveActivity = () => {
  $q.notify({
    color: 'orange',
    message: '已设置活动开始提醒！',
    icon: 'notifications_active',
    position: 'top',
    timeout: 2000,
  });
};

// 模拟活动数据
const activitiesData = [
  {
    id: 1,
    title: '夏日特惠：全场商品8折起',
    description: '炎炎夏日，清凉特惠！全场商品8折起，部分热门商品低至5折，还有限量夏日礼包等你来拿！',
    startDate: '2023-07-01',
    endDate: '2023-08-15',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    tags: ['限时折扣', '全场优惠'],
    isHot: true,
    participants: 1256,
    status: 'ended',
    details: `<p>炎炎夏日，清凉特惠！全场商品8折起，部分热门商品低至5折，还有限量夏日礼包等你来拿！</p>
    <p>活动期间，凡在本平台购物满300元，即可获得夏日清凉礼包一份，包含便携小风扇、防晒霜、冰袖等夏季必备好物。</p>
    <p>此外，指定品类商品享受额外优惠：</p>
    <ul>
      <li>夏季服饰：全场7折</li>
      <li>防晒用品：满200减50</li>
      <li>户外装备：满500减100</li>
      <li>冰饮茶品：第二件半价</li>
    </ul>
    <p>活动商品数量有限，先到先得，售完即止！</p>`,
    rules: `<h3>活动规则</h3>
    <ol>
      <li>活动时间：2023年7月1日至2023年8月15日</li>
      <li>活动期间，全场商品享受不同程度的折扣，具体折扣以商品页面显示为准</li>
      <li>部分特价商品和新品不参与折扣活动</li>
      <li>满300元赠送的夏日礼包，每个账户限领一份</li>
      <li>所有优惠不可叠加使用</li>
      <li>活动最终解释权归本平台所有</li>
    </ol>`,
    prizes: `<h3>奖励说明</h3>
    <p>活动期间，用户可获得以下奖励：</p>
    <ol>
      <li>购物满300元：获得夏日清凉礼包一份（价值99元）</li>
      <li>购物满500元：额外获得50元无门槛优惠券一张</li>
      <li>购物满1000元：额外获得100元无门槛优惠券一张</li>
      <li>活动期间累计购物金额排名前10名的用户：获得价值299元的夏日豪华礼盒一份</li>
    </ol>`,
    faqs: [
      {
        question: '活动优惠是否可以叠加使用？',
        answer: '不可以，本次活动的各项优惠不可叠加使用，系统将自动为您选择最优惠的方案。',
      },
      {
        question: '夏日礼包如何领取？',
        answer: '符合条件的用户下单后，系统将自动将礼包添加到订单中，无需额外操作。',
      },
      {
        question: '退货后是否影响活动奖励？',
        answer: '是的，如果退货后订单金额不满足活动条件，需要同时退还对应的礼品或优惠金额。',
      },
      {
        question: '活动商品是否支持7天无理由退换？',
        answer: '支持，但部分特价商品除外，具体以商品页面说明为准。',
      },
    ],
    products: [
      {
        id: 101,
        name: '夏季轻薄防晒衣',
        image: 'https://cdn.quasar.dev/img/mountains.jpg',
        originalPrice: 19900,
        activityPrice: 9950,
      },
      {
        id: 102,
        name: '便携式折叠风扇',
        image: 'https://cdn.quasar.dev/img/parallax1.jpg',
        originalPrice: 5900,
        activityPrice: 4130,
      },
      {
        id: 103,
        name: '高倍防晒霜SPF50',
        image: 'https://cdn.quasar.dev/img/parallax2.jpg',
        originalPrice: 12900,
        activityPrice: 9030,
      },
      {
        id: 104,
        name: '冰丝防晒袖套',
        image: 'https://cdn.quasar.dev/img/quasar.jpg',
        originalPrice: 3900,
        activityPrice: 2730,
      },
    ],
  },
  {
    id: 2,
    title: '开学季：学生专享优惠',
    description: '开学季来临，学生专享优惠！凭学生证购买指定商品享受额外9折优惠，还有机会赢取iPad等学习用品！',
    startDate: '2023-08-15',
    endDate: '2023-09-15',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    tags: ['学生专享', '数码产品'],
    isHot: true,
    participants: 876,
    status: 'ended',
    details: `<p>开学季来临，学生专享优惠！凭学生证购买指定商品享受额外9折优惠，还有机会赢取iPad等学习用品！</p>
    <p>活动期间，学生用户可享受以下优惠：</p>
    <ul>
      <li>数码产品：额外9折优惠</li>
      <li>文具用品：满100减30</li>
      <li>书籍：满200减50</li>
      <li>学习桌椅：满500减100</li>
    </ul>
    <p>此外，活动期间购买学习用品满300元的学生用户，即可参与抽奖，有机会赢取iPad、AirPods、kindle等学习好物！</p>`,
    rules: `<h3>活动规则</h3>
    <ol>
      <li>活动时间：2023年8月15日至2023年9月15日</li>
      <li>活动仅限学生用户参与，需在下单时上传学生证照片进行验证</li>
      <li>每个学生用户限享一次额外9折优惠</li>
      <li>部分特价商品和新品不参与折扣活动</li>
      <li>抽奖活动每人限参与一次，中奖名单将在活动结束后公布</li>
      <li>活动最终解释权归本平台所有</li>
    </ol>`,
    prizes: `<h3>奖励说明</h3>
    <p>活动期间，学生用户可获得以下奖励：</p>
    <ol>
      <li>购物满300元：参与抽奖活动，奖品包括：</li>
      <ul>
        <li>一等奖（3名）：iPad 10.2英寸</li>
        <li>二等奖（10名）：AirPods</li>
        <li>三等奖（20名）：kindle</li>
        <li>四等奖（50名）：50元无门槛优惠券</li>
        <li>五等奖（100名）：20元无门槛优惠券</li>
      </ul>
      <li>购物满500元：额外获得学习礼包一份（价值99元）</li>
      <li>购物满1000元：额外获得100元无门槛优惠券一张</li>
    </ol>`,
    faqs: [
      {
        question: '如何验证学生身份？',
        answer: '在下单时，系统会提示上传学生证照片，审核通过后即可享受学生优惠。',
      },
      {
        question: '学生优惠是否可以与其他优惠叠加？',
        answer: '学生额外9折优惠可以与平台其他满减活动叠加使用，但不能与其他折扣优惠叠加。',
      },
      {
        question: '抽奖活动什么时候公布结果？',
        answer: '抽奖活动将在活动结束后3个工作日内公布结果，中奖用户将收到站内信通知。',
      },
      {
        question: '非学生用户可以参与活动吗？',
        answer: '非学生用户可以正常购买活动商品，但不能享受学生专享的额外9折优惠和抽奖活动。',
      },
    ],
    products: [
      {
        id: 201,
        name: 'iPad 10.2英寸',
        image: 'https://cdn.quasar.dev/img/mountains.jpg',
        originalPrice: 259900,
        activityPrice: 233910,
      },
      {
        id: 202,
        name: '无线蓝牙耳机',
        image: 'https://cdn.quasar.dev/img/parallax1.jpg',
        originalPrice: 19900,
        activityPrice: 17910,
      },
      {
        id: 203,
        name: '智能手表',
        image: 'https://cdn.quasar.dev/img/parallax2.jpg',
        originalPrice: 89900,
        activityPrice: 80910,
      },
      {
        id: 204,
        name: '机械键盘',
        image: 'https://cdn.quasar.dev/img/quasar.jpg',
        originalPrice: 29900,
        activityPrice: 26910,
      },
    ],
  },
  {
    id: 10,
    title: '618年中购物节',
    description: '年中大促，好物半价起！全场商品低至5折，每满300减50，抢先预售享额外9折，618购物狂欢等你来！',
    startDate: '2024-06-01',
    endDate: '2024-06-18',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    tags: ['618特惠', '全场优惠'],
    isHot: true,
    participants: 6789,
    status: 'ongoing',
    details: `<p>年中大促，好物半价起！全场商品低至5折，每满300减50，抢先预售享额外9折，618购物狂欢等你来！</p>
    <p>活动期间，平台将推出多重优惠活动：</p>
    <ul>
      <li>全场商品低至5折</li>
      <li>每满300减50，上不封顶</li>
      <li>抢先预售享额外9折</li>
      <li>限时秒杀，每天10点、14点、20点准时开抢</li>
      <li>购物满1000元，赠送618限定礼盒一份</li>
    </ul>
    <p>此外，活动期间下单还可参与"618幸运抽奖"，有机会赢取iPhone、iPad等大奖！</p>`,
    rules: `<h3>活动规则</h3>
    <ol>
      <li>活动时间：2024年6月1日至2024年6月18日</li>
      <li>活动期间，全场商品享受不同程度的折扣，具体折扣以商品页面显示为准</li>
      <li>满减活动（每满300减50）与折扣可叠加使用</li>
      <li>部分特价商品不参与满减活动</li>
      <li>每个用户限领一份618限定礼盒</li>
      <li>幸运抽奖活动每人每天限参与3次</li>
      <li>活动最终解释权归本平台所有</li>
    </ol>`,
    prizes: `<h3>奖励说明</h3>
    <p>活动期间，用户可获得以下奖励：</p>
    <ol>
      <li>购物满1000元：获得618限定礼盒一份（价值199元）</li>
      <li>购物满2000元：额外获得200元无门槛优惠券一张</li>
      <li>购物满3000元：额外获得300元无门槛优惠券一张</li>
      <li>幸运抽奖奖品：</li>
      <ul>
        <li>特等奖（3名）：iPhone 14 Pro</li>
        <li>一等奖（10名）：iPad Air</li>
        <li>二等奖（50名）：AirPods Pro</li>
        <li>三等奖（100名）：100元无门槛优惠券</li>
        <li>四等奖（1000名）：50元无门槛优惠券</li>
        <li>五等奖（10000名）：10元无门槛优惠券</li>
      </ul>
    </ol>`,
    faqs: [
      {
        question: '满减活动是否可以与折扣叠加？',
        answer: '可以，满减活动（每满300减50）与商品折扣可以叠加使用，系统会自动计算最终价格。',
      },
      {
        question: '618限定礼盒包含哪些商品？',
        answer: '618限定礼盒包含精美手账本、保温杯、帆布袋、贴纸等多种实用好物，总价值199元。',
      },
      {
        question: '如何参与幸运抽奖活动？',
        answer: '活动期间下单后，系统会自动发放抽奖机会，您可以在"我的-抽奖活动"中参与抽奖。',
      },
      {
        question: '优惠券有效期是多久？',
        answer: '本次活动发放的优惠券有效期为30天，请在有效期内使用。',
      },
    ],
    products: [
      {
        id: 1001,
        name: '智能手机',
        image: 'https://cdn.quasar.dev/img/mountains.jpg',
        originalPrice: 399900,
        activityPrice: 199950,
      },
      {
        id: 1002,
        name: '无线耳机',
        image: 'https://cdn.quasar.dev/img/parallax1.jpg',
        originalPrice: 99900,
        activityPrice: 49950,
      },
      {
        id: 1003,
        name: '智能手表',
        image: 'https://cdn.quasar.dev/img/parallax2.jpg',
        originalPrice: 149900,
        activityPrice: 74950,
      },
      {
        id: 1004,
        name: '平板电脑',
        image: 'https://cdn.quasar.dev/img/quasar.jpg',
        originalPrice: 299900,
        activityPrice: 149950,
      },
    ],
  },
];
</script>

<style lang="scss" scoped>
.activity-detail-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.activity-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 16px;
}

.activity-content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.activity-header {
  margin-bottom: 20px;
}

.activity-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.activity-status-badge {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  color: white;

  &.status-upcoming {
    background-color: #ff9800;
  }

  &.status-ongoing {
    background-color: #4caf50;
  }

  &.status-ended {
    background-color: #9e9e9e;
  }
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
  color: #666;
  font-size: 14px;

  .meta-item {
    display: flex;
    align-items: center;
  }

  .countdown {
    color: #ff5722;
    font-weight: bold;
  }
}

.activity-cover {
  margin: 24px 0;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.subsection-title {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin: 24px 0 16px;
}

.activity-products {
  margin-top: 30px;
}

.product-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.product-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  .current-price {
    font-size: 16px;
    font-weight: bold;
    color: #ff5722;
  }

  .original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
    margin-left: 5px;
  }
}

.discount-tag {
  background-color: #ff5722;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.activity-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.social-share {
  display: flex;
  align-items: center;
}

.related-activities {
  margin-top: 40px;
}

.activity-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.activity-image-container {
  position: relative;
}

.activity-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 0;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 767px) {
  .activity-content-wrapper {
    padding: 16px;
  }

  .activity-title {
    font-size: 22px;
  }

  .activity-meta {
    font-size: 13px;
    gap: 12px;
  }

  .section-title {
    font-size: 20px;
  }

  .subsection-title {
    font-size: 16px;
  }
}
</style>
