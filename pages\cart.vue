<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="cart-page q-pa-md">
    <!-- 备注弹窗 - 美化版 -->
    <q-dialog v-model="memoDialog" persistent transition-show="scale" transition-hide="scale">
      <q-card class="memo-dialog-card">
        <q-card-section class="memo-dialog-header row items-center">
          <q-icon name="edit_note" size="24px" color="primary" class="q-mr-sm" />
          <div class="text-h6 text-weight-medium">编辑商品备注</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup class="memo-close-btn" />
        </q-card-section>

        <q-separator />

        <q-card-section class="q-pt-lg">
          <p class="text-grey-8 q-mb-sm memo-description">
            <q-icon name="info" size="16px" color="grey-7" class="q-mr-xs" />
            添加备注信息，方便您记录商品特殊要求或购买原因
          </p>
          <q-input
            v-model="currentMemo"
            type="textarea"
            autofocus
            outlined
            maxlength="200"
            counter
            rows="4"
            placeholder="例如：请发货时包装牢固，或者这是送给朋友的礼物..."
            class="full-width memo-input"
            :rules="[(val) => val.length <= 200 || '备注不能超过200字']"
            bg-color="white">
            <template #prepend>
              <q-icon name="sticky_note_2" color="primary" />
            </template>
          </q-input>
        </q-card-section>

        <q-card-actions align="right" class="q-pb-md q-px-md">
          <q-btn flat label="取消" color="grey-7" v-close-popup class="memo-cancel-btn" />
          <q-btn unelevated label="保存备注" color="primary" icon="save" @click="saveMemo" v-close-popup class="memo-save-btn" />
        </q-card-actions>
      </q-card>
    </q-dialog>
    <div class="cart-header row items-center justify-between q-py-sm">
      <div class="col-auto">
        <h4 class="text-h5 q-my-none">购物车</h4>
      </div>
      <div class="col-auto">
        <q-btn flat color="primary" label="继续购物" icon="arrow_back" icon-right="shopping_bag" @click="navigateTo('/')" class="continue-shopping-btn" />
      </div>
    </div>

    <!-- 桌面端表格视图 -->
    <div class="desktop-view">
      <!-- 表格标题行 -->
      <div class="row items-center q-py-sm bg-blue-1 text-blue-9 table-header rounded-borders">
        <div class="col-1"></div>
        <div class="col-4 text-center text-weight-medium">商品信息</div>
        <div class="col-2 text-center text-weight-medium">备注</div>
        <div class="col-1 text-center text-weight-medium">单价</div>
        <div class="col-2 text-center text-weight-medium">数量</div>
        <div class="col-1 text-center text-weight-medium">金额</div>
        <div class="col-1 text-center text-weight-medium">编辑</div>
      </div>

      <div v-if="cartList.length === 0" class="text-center">
        <q-icon name="shopping_cart" class="q-my-xl" size="100px" color="grey-4" />
        <p class="text-grey-6 text-h6 q-my-xl">您的购物车为空</p>
      </div>
      <div v-else style="min-height: 300px">
        <!-- 店铺分组展示商品 -->
        <ShopCartGroup
          v-for="(shop, shopIndex) in cartList"
          :key="shopIndex"
          :shop="shop"
          :shop-index="shopIndex"
          :is-first-group="shopIndex === 0"
          @open-memo-dialog="openMemoDialog"
          @decrease-quantity="decreaseQuantity"
          @increase-quantity="increaseQuantity"
          @update-quantity="updateQuantity"
          @delete-item="deleteItem"
          @item-select-change="checkAllSelected" />
      </div>
    </div>

    <!-- 移动端卡片视图 -->
    <div class="mobile-view">
      <div v-if="cartList.length === 0" class="text-center">
        <q-icon name="shopping_cart" class="q-my-xl" size="80px" color="grey-4" />
        <p class="text-grey-6 text-subtitle1 q-my-xl">您的购物车为空</p>
      </div>
      <div v-else>
        <!-- 店铺分组展示商品 -->
        <ShopCartGroup
          v-for="(shop, shopIndex) in cartList"
          :key="shopIndex"
          :shop="shop"
          :shop-index="shopIndex"
          :is-first-group="shopIndex === 0"
          @open-memo-dialog="openMemoDialog"
          @decrease-quantity="decreaseQuantity"
          @increase-quantity="increaseQuantity"
          @update-quantity="updateQuantity"
          @delete-item="deleteItem"
          @item-select-change="checkAllSelected" />
      </div>
    </div>

    <!-- 桌面端结算区域 -->
    <div class="desktop-view checkout-section q-mt-md">
      <div class="row items-center bg-grey-2 q-pa-md rounded-borders">
        <div class="col-6">
          <q-checkbox :model-value="allSelected" dense @update:model-value="toggleAllSelect" label="全选" class="q-ml-sm" />
          <span class="inline-block q-ml-md">
            已选中商品 <span class="text-bold text-red">{{ totalItems }}</span> 件
          </span>
        </div>

        <div class="col-6">
          <div class="row items-center justify-end">
            <div class="column items-end q-mr-md price-summary">
              <div class="row items-center q-mb-xs no-wrap price-row">
                <span class="text-subtitle2 label-fixed-width">商品费用：</span>
                <span class="text-subtitle1 text-red text-weight-medium amount-column" v-html="formatAmount(totalPrice)"></span>
              </div>
              <div class="row items-center no-wrap price-row">
                <span class="text-subtitle2 label-fixed-width">运费：</span>
                <span class="text-subtitle1 text-red text-weight-medium amount-column" v-html="formatAmount(totalFreight)"></span>
              </div>
            </div>

            <div class="checkout-total q-px-md q-py-sm">
              <div class="text-subtitle1 no-wrap total-row">
                <span class="label-fixed-width">合计：</span>
                <span class="text-h6 text-red text-weight-bold amount-column" v-html="formatAmount(totalAmount)"></span>
              </div>
              <q-btn color="primary" label="去结算" icon-right="arrow_forward" @click="checkout" class="q-mt-xs checkout-btn" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端结算区域 -->
    <div class="mobile-view checkout-bar">
      <div class="column q-pa-md bg-white">
        <!-- 第一行：全选和已选信息 -->
        <div class="row items-center justify-start q-mb-sm">
          <q-checkbox :model-value="allSelected" dense @update:model-value="toggleAllSelect" label="全选" class="mobile-select-all" />
          <span class="q-ml-sm text-caption">
            已选 <span class="text-red text-weight-medium">{{ totalItems }}</span> 件
          </span>
        </div>

        <!-- 第二行：金额信息和结算按钮 -->
        <div class="row items-center justify-end">
          <div class="mobile-total-container">
            <div class="text-subtitle2 no-wrap mobile-price-row">
              <span class="mobile-label">合计:</span>
              <span class="text-red text-weight-bold mobile-amount" v-html="formatAmount(totalAmount)"></span>
            </div>
            <div class="text-caption q-mt-xs text-grey-7 no-wrap mobile-price-row">
              <span class="mobile-label">含运费:</span>
              <span class="mobile-amount" v-html="formatAmount(totalFreight)"></span>
            </div>
          </div>
          <q-btn color="primary" label="去结算" @click="checkout" class="mobile-checkout-btn q-ml-sm" :disable="totalItems === 0" />
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { useCartStore } from '~/store/cart';
import { useOrderConfirmStore } from '~/store/orderConfirm';
import ShopCartGroup from '~/components/cart/ShopCartGroup.vue';
import { useCurrency } from '~/composables/useCurrency';

const breadcrumbs = [{ label: '购物车', to: '/cart' }];
const cartStore = useCartStore();
const orderConfirmStore = useOrderConfirmStore();
const { formatAmount } = useCurrency();

// 备注弹窗相关
const memoDialog = ref(false);
const currentMemo = ref('');
const currentItemIndices = ref({ shopIndex: -1, itemIndex: -1 });

const cartList = computed(() => formatCartData(cartStore.list));

onMounted(() => {
  //获取购物车列表
  cartStore.getList();
});

// 全选状态
const allSelected = computed(() => cartList.value.every((shop) => shop.items.every((item) => item.selected)));

// 总价格与数量
const totalPrice = computed(() => cartStore.cartTotalAmount);
const totalItems = computed(() => cartStore.cartTotalCount);
const totalFreight = computed(() => cartStore.cartTotalFreight);
const totalAmount = computed(() => cartStore.cartTotalFreight + cartStore.cartTotalAmount);

// 全选/反选
const toggleAllSelect = () => {
  const newSelected = !allSelected.value; // 反选或全选
  cartStore.toggleAllSelect(newSelected);
};

// 增加数量
const increaseQuantity = (shopIndex, itemIndex) => {
  console.log('增加数量:shopIndex:', shopIndex, 'itemIndex:', itemIndex);
  const item = cartList.value[shopIndex].items[itemIndex];
  if (item.count + 1 > item.sku.stock) {
    return;
  }
  useCartStore().updateCartQuantity({
    product: item,
    qty: 1,
  });
};
//减少数量
const decreaseQuantity = (shopIndex, itemIndex) => {
  console.log('减少数量:shopIndex:', shopIndex, 'itemIndex:', itemIndex);
  const item = cartList.value[shopIndex].items[itemIndex];
  if (item.count > 1) {
    // item.count--;
    useCartStore().updateCartQuantity({
      product: item,
      qty: -1,
    });
  }
};

const updateQuantity = (shopIndex, itemIndex, newValue) => {
  const newQuantity = Math.max(1, parseInt(newValue, 10) || 1);
  console.log('更新数量:shopIndex:', shopIndex, 'itemIndex:', itemIndex, 'e:', newValue);
  const item = cartList.value[shopIndex].items[itemIndex];
  useCartStore().updateCartQuantity({
    product: item,
    qty: newQuantity - item.count,
  });
};

// 删除单个商品
const deleteItem = (shopIndex, itemIndex) => {
  const item = cartList.value[shopIndex].items[itemIndex];
  cartStore.removeCartItem(item);
};

// 结算按钮
const checkout = () => {
  if (cartStore.list.length == 0) {
    useNuxtApp().$showNotify({ msg: '先去购物吧', type: 'warning' });

    return;
  }

  if (!cartStore.cartSelectIds.length) {
    useNuxtApp().$showNotify({ msg: '请选择要结算的商品', type: 'warning' });
    return;
  }

  //把已选的购物车ID存储到订单确认页的store
  orderConfirmStore.selectIds = cartStore.cartSelectIds;
  // 跳转到订单确认页面
  navigateTo('/order/confirm');
};

//格式化购物车数据按商店分组
function formatCartData(cartItems) {
  // 创建一个 Map 用于分组存储
  const groupedData = new Map();

  // 遍历所有购物车项
  cartItems.forEach((item) => {
    if (!item.spu.shopName) {
      item.spu.shopName = '未分组';
    }
    // 如果当前 shopName 不存在于 Map，则创建一个新分组
    if (!groupedData.has(item.spu.shopName)) {
      groupedData.set(item.spu.shopName, {
        shopName: item.spu.shopName || '未分组',
        items: [],
        freight: 0, // 初始化最大运费
      });
    }

    // 将商品添加到对应分组的 items 数组中
    // groupedData.get(item.spu.shopName).items.push(item);

    const shopGroup = groupedData.get(item.spu.shopName);

    // 更新最大运费
    shopGroup.freight = Math.max(shopGroup.freight, item.spu.freight || 0);

    // 将商品添加到对应分组的 items 数组中
    shopGroup.items.push(item);
  });

  // 转换 Map 为数组返回
  return Array.from(groupedData.values());
}

// 打开备注弹窗
const openMemoDialog = (shopIndex, itemIndex) => {
  const item = cartList.value[shopIndex].items[itemIndex];
  currentMemo.value = item.memo || '';
  currentItemIndices.value = { shopIndex, itemIndex };
  memoDialog.value = true;
};

// 保存备注
const saveMemo = () => {
  const { shopIndex, itemIndex } = currentItemIndices.value;
  if (shopIndex >= 0 && itemIndex >= 0) {
    const item = cartList.value[shopIndex].items[itemIndex];

    // 调用API更新备注
    cartStore.updateCartMemo({
      id: item.id,
      memo: currentMemo.value,
    });

    // 更新本地数据
    item.memo = currentMemo.value;

    // 显示成功提示
    useNuxtApp().$showNotify({ msg: '备注已更新', type: 'positive' });
  }

  // 重置状态
  memoDialog.value = false;
  currentMemo.value = '';
  currentItemIndices.value = { shopIndex: -1, itemIndex: -1 };
};

// 检查是否全选
const checkAllSelected = () => {
  // 这个方法会在商品选择状态变化时被调用
  // allSelected 是一个计算属性，会自动更新
};
</script>

<style scoped lang="scss">
@import '~/assets/styles/cart.scss';

.cart-page {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

// 金额列样式 - 与订单页面保持一致
.amount-column {
  display: inline-block;
  min-width: 150px; // 进一步增加最小宽度，确保金额较长时不会换行
  text-align: right;
  white-space: nowrap; // 确保金额不会换行
  overflow: visible; // 确保内容不会被裁剪

  @media (max-width: 991px) {
    min-width: 130px;
  }

  @media (max-width: 767px) {
    min-width: 110px;
  }

  @media (max-width: 599px) {
    min-width: 100px;
    font-size: 0.9rem;
  }

  .primary-currency {
    white-space: nowrap;
    display: inline-block;

    :deep(.default-currency) {
      font-size: 0.8em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: normal;
    }
  }
}

// 标签固定宽度，防止换行
.label-fixed-width {
  min-width: 90px; // 增加标签宽度
  display: inline-block;
  white-space: nowrap;
  text-align: left; // 确保文本左对齐

  @media (max-width: 767px) {
    min-width: 70px;
  }

  @media (max-width: 599px) {
    min-width: 60px;
    font-size: 0.9rem;
  }
}

// 移动端价格行样式
.mobile-price-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

// 移动端标签样式
.mobile-label {
  min-width: 60px; // 增加标签宽度
  white-space: nowrap;
  margin-right: 4px;
  text-align: left; // 确保文本左对齐
}

// 移动端金额样式
.mobile-amount {
  min-width: 130px; // 进一步增加金额宽度，为多币种显示提供更充足的空间
  text-align: right;
  white-space: nowrap;
  overflow: visible; // 确保内容不会被裁剪

  @media (max-width: 480px) {
    min-width: 120px;
  }

  @media (max-width: 400px) {
    min-width: 110px;
  }
}

// 移动端结算区域包装器
.mobile-checkout-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  @media (max-width: 599px) {
    flex-wrap: nowrap;
  }

  @media (max-width: 400px) {
    flex-direction: row;
  }
}

// 移动端合计容器
.mobile-total-container {
  min-width: 200px; // 增加容器宽度，为多币种显示提供更充足的空间
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  @media (max-width: 480px) {
    min-width: 180px;
  }

  @media (max-width: 400px) {
    min-width: 160px;
  }
}

.cart-header {
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 16px;

  h4 {
    position: relative;
    padding-left: 12px;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      background-color: #1976d2;
      border-radius: 2px;
    }
  }

  .continue-shopping-btn {
    border: 1px solid #1976d2;
    border-radius: 4px;
    padding: 4px 12px;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(25, 118, 210, 0.1);
    }
  }
}

.text-bold {
  font-weight: bold;
}

.nowrap {
  white-space: nowrap;
}

.border {
  border-top: 1px solid #e0e0e0;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}

.border:nth-last-child(1) {
  border-bottom: 1px solid #e0e0e0;
}

.multiline-ellipsis {
  display: -webkit-box; /* 必须搭配 */
  -webkit-box-orient: vertical; /* 垂直方向排列子元素 */
  -webkit-line-clamp: 2; /* 限制显示两行 */
  line-clamp: 2; /* 标准属性 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: normal; /* 允许换行 */
  max-width: 100%; /* 设置宽度限制，避免超出 */
  word-break: break-word; /* 防止长单词溢出 */
}

.multiline-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  max-width: 100%;
  word-break: break-word;
}

.multiline-ellipsis-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  max-width: 100%;
  word-break: break-word;
}

.multiline-ellipsis-3 {
  display: -webkit-box; /* 必须搭配 */
  -webkit-box-orient: vertical; /* 垂直方向排列子元素 */
  -webkit-line-clamp: 3; /* 限制显示三行 */
  line-clamp: 3; /* 标准属性 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: normal; /* 允许换行 */
  max-width: 100%; /* 设置宽度限制，避免超出 */
  word-break: break-word; /* 防止长单词溢出 */
}

.cutline-right {
  border-right: 1px solid #bdbdbd;
}

/* 响应式样式 */
.desktop-view {
  display: block;
}

.mobile-view {
  display: none;
}

.cart-item-card {
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

.table-header {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.shop-header {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.checkout-section {
  .price-summary {
    min-width: 250px; // 确保价格摘要区域有足够的宽度

    @media (max-width: 991px) {
      min-width: 220px;
    }

    .price-row {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      overflow: visible;
    }
  }

  .checkout-total {
    background-color: #f5f5f5;
    border-left: 1px solid #e0e0e0;
    border-radius: 0 4px 4px 0;
    min-width: 250px; // 进一步增加宽度，确保有足够的空间显示金额

    @media (max-width: 991px) {
      min-width: 220px;
    }

    @media (max-width: 767px) {
      min-width: 200px;
    }

    .total-row {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      overflow: visible;
    }
  }

  .checkout-btn {
    width: 100%;
    font-weight: 500;
  }
}

.checkout-bar {
  position: sticky;
  bottom: 0;
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  border-top: 1px solid #e0e0e0;

  .mobile-checkout-btn {
    min-width: 90px;
    white-space: nowrap;
    height: 40px; // 增加按钮高度
    font-size: 14px;
    padding: 0 20px; // 增加按钮内边距
    border-radius: 20px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin-left: 12px; // 增加与金额信息的间距

    @media (max-width: 480px) {
      min-width: 80px;
      padding: 0 16px;
    }

    @media (max-width: 400px) {
      min-width: 70px;
      padding: 0 12px;
    }

    &:not(:disabled) {
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
      }
    }

    &:disabled {
      opacity: 0.7;
    }
  }

  .mobile-select-all {
    transform: scale(0.9);
    margin-left: -4px;
  }
}

/* 平板电脑样式 */
@media (max-width: 1023px) {
  .cart-page {
    padding: 8px !important;
  }

  .desktop-view {
    display: none;
  }

  .mobile-view {
    display: block;
  }
}

/* 移动端商品卡片样式 */
.mobile-cart-item {
  margin-bottom: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

/* 备注弹窗样式 */
.memo-dialog-card {
  min-width: 400px;
  max-width: 90vw;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);

  @media (max-width: 599px) {
    min-width: 90vw;
  }
}

.memo-dialog-header {
  padding: 16px 20px;
  background-color: #f5f7fa;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.memo-description {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.memo-input {
  .q-field__control {
    min-height: 120px;
  }

  .q-field__native {
    padding: 12px;
    font-size: 14px;
    line-height: 1.5;
  }

  .q-field__counter {
    color: #9e9e9e;
    font-size: 12px;
  }
}

.memo-save-btn {
  padding: 8px 16px;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
    transform: translateY(-1px);
  }
}

.memo-cancel-btn {
  border-radius: 4px;
  margin-right: 8px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.memo-close-btn {
  opacity: 0.7;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.mobile-checkbox {
  margin: 0;
  padding: 0;
  transform: scale(0.9);
}

.mobile-product-img {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }
}

.mobile-product-name {
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 2px;
  max-height: 2.8em;
  font-weight: 500;
  color: #333;

  a {
    color: inherit;
    text-decoration: none;

    &:hover {
      color: var(--q-primary);
      text-decoration: underline;
    }
  }
}

.mobile-product-props {
  font-size: 12px;
  line-height: 1.2;
  margin: 0;
  color: #777;
}

.price-row {
  border-top: 1px dashed #e0e0e0;
  padding-top: 8px;

  .row.no-wrap {
    min-height: 24px;
  }

  .price-label {
    font-size: 12px;
    line-height: 1.2;
    font-weight: 500;
    white-space: nowrap;
  }

  .price-value {
    font-size: 13px;
    line-height: 1.4;
    font-weight: 500;
    white-space: nowrap;
  }
}

.quantity-controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
}

.mobile-qty-btn {
  min-height: 20px;
  min-width: 20px;
  height: 20px;
  width: 20px;
  padding: 0;
  margin: 0;

  .q-icon {
    font-size: 14px;
  }

  :deep(.q-btn__wrapper) {
    padding: 0;
    min-height: 20px;
  }
}

.custom-qty-input {
  width: 36px;
  height: 20px;
  margin: 0 2px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  text-align: center;
  font-size: 12px;
  line-height: 1;
  padding: 0;
  outline: none;

  &:focus {
    border-color: #1976d2;
  }
}

.mobile-delete-btn {
  min-height: 28px;
  padding: 0 8px;
  border: 1px solid #ff5252;
  border-radius: 4px;

  :deep(.q-btn__wrapper) {
    padding: 4px 8px;
    min-height: 28px;
  }

  span {
    font-size: 12px;
  }
}

.memo-row {
  border-top: 1px dashed #e0e0e0;
  padding-top: 8px;

  .memo-label {
    font-size: 12px;
    line-height: 1.2;
    font-weight: 500;
  }

  .memo-content {
    font-size: 12px;
    line-height: 1.4;
    max-width: 100%;
    word-break: break-word;
  }
}

/* 手机样式 */
@media (max-width: 599px) {
  .cart-page {
    padding: 4px !important;
  }

  .cart-header {
    margin-bottom: 8px;

    h4 {
      font-size: 16px;
    }

    .continue-shopping-btn {
      font-size: 12px;
      padding: 2px 8px;
    }
  }

  .shop-header {
    padding: 4px 8px !important;
    font-size: 12px;
  }

  .quantity-controls {
    .q-btn {
      padding: 0 2px;
    }
  }
}
</style>
