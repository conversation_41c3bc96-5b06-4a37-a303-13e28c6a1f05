const BlogApi = {
  // 获得blog分页
  getBlogPage: (params) => {
    return useServerGet('/promotion/article/page', {
      params,
    });
  },

  // 获得blog详情
  getBlogDetail: (params) => {
    return useServerGet('/promotion/article/get', {
      params,
    });
  },

  // 增加浏览次数
  incrementViews: (params) => {
    return useClientPut('/promotion/article/add-browse-count', {
      params,
    });
  },
};

export default BlogApi;
