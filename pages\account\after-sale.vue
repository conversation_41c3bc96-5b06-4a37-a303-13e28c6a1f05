<template>
  <div class="after-sale-list-page">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm rounded-borders">
      <div class="row justify-between items-center">
        <div class="row items-center">
          <q-btn flat dense color="primary" icon="arrow_back" @click="router.push('/account')" class="q-mr-sm" />
          <span class="text-subtitle1 text-weight-medium">售后列表</span>
        </div>
        <q-btn flat dense color="primary" to="/account" label="返回首页" class="q-px-sm" />
      </div>
    </div>

    <div class="q-pa-md">
      <!-- Tab 选项卡 -->
      <q-tabs
        v-model="state.currentTab"
        class="text-primary q-mb-md"
        active-color="primary"
        indicator-color="primary"
        align="left"
        narrow-indicator
        :dense="$q.screen.lt.sm"
        @update:model-value="onTabsChange">
        <q-tab name="all" label="全部" />
        <q-tab name="processing" label="处理中" />
        <q-tab name="completed" label="已完成" />
        <q-tab name="cancelled" label="已取消" />
      </q-tabs>

      <!-- 空状态 -->
      <div v-if="state.pagination.total === 0 && state.loadStatus !== 'loading'" class="text-center q-py-xl">
        <q-icon name="receipt_long" size="64px" color="grey-4" />
        <div class="text-grey-6 q-mt-md">暂无售后记录</div>
      </div>

      <!-- 加载状态 -->
      <div v-if="state.loadStatus === 'loading'" class="text-center q-py-xl">
        <q-spinner color="primary" size="40px" />
        <div class="text-grey-6 q-mt-md">加载中...</div>
      </div>

      <!-- 列表 -->
      <div v-if="state.pagination.total > 0" class="column q-gutter-md">
        <!-- 桌面视图 -->
        <div v-if="!$q.screen.lt.sm">
          <q-card v-for="order in state.pagination.list" :key="order.id" class="after-sale-card cursor-pointer" @click="goToDetail(order.id)">
            <!-- 卡片头部 -->
            <q-card-section class="bg-grey-1 q-py-sm q-px-md">
              <div class="row justify-between items-center">
                <div class="text-body2">
                  <span class="text-weight-medium">服务单号：</span>
                  <span class="text-primary">{{ order.no }}</span>
                  <span class="q-mx-md text-grey-7">{{ formatDateTime(order.createTime) }}</span>
                </div>
                <q-badge :color="getAfterSaleStatusColor(order.status)" class="q-py-xs q-px-sm">
                  {{ formatAfterSaleStatus(order) }}
                </q-badge>
              </div>
            </q-card-section>

            <q-separator />

            <!-- 商品信息 -->
            <q-card-section class="q-pa-md">
              <div class="row no-wrap items-start">
                <!-- 商品图片 -->
                <div class="product-image q-mr-md">
                  <q-img :src="order.picUrl || '/images/placeholder.png'" style="width: 80px; height: 80px" class="rounded-borders" fit="cover" spinner-color="primary" />
                </div>
                <!-- 商品信息 -->
                <div class="column justify-between flex-1">
                  <div class="text-body1 text-weight-medium q-mb-xs">{{ order.spuName }}</div>
                  <div class="text-caption text-grey-7 q-mb-sm">
                    {{ order.properties?.map((property) => property.valueName).join(' ') }}
                  </div>
                  <div class="row justify-between items-center">
                    <div class="text-body2 text-weight-medium text-primary">退款金额：{{ formatAmount(order.refundPrice) }}</div>
                    <div class="text-caption text-grey-7">数量: {{ order.count }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <!-- 售后信息和操作 -->
            <q-card-section class="q-pa-md">
              <div class="row justify-between items-center">
                <div class="column">
                  <div class="text-body2 text-weight-medium">{{ formatAfterSaleWay(order.way) }}</div>
                  <div class="text-caption text-grey-7">{{ formatAfterSaleStatusDescription(order) }}</div>
                </div>
                <div class="row q-gutter-sm">
                  <q-btn v-if="order.buttons?.includes('cancel')" outline color="negative" size="sm" label="取消申请" @click.stop="onCancel(order.id)" />
                  <q-btn v-if="order.buttons?.includes('delivery')" outline color="primary" size="sm" label="填写退货" @click.stop="onDelivery(order.id)" />
                  <q-btn flat color="primary" size="sm" label="查看详情" icon-right="arrow_forward" @click.stop="goToDetail(order.id)" />
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 移动端视图 -->
        <div v-else class="mobile-after-sale-list">
          <q-card v-for="order in state.pagination.list" :key="order.id" class="after-sale-card-mobile q-mb-md cursor-pointer" @click="goToDetail(order.id)">
            <!-- 卡片头部 -->
            <q-card-section class="after-sale-header q-py-sm">
              <div class="row justify-between items-center">
                <div class="after-sale-no">
                  <span class="text-weight-medium">服务单号</span>
                  <span class="text-primary">{{ order.no }}</span>
                </div>
                <q-badge :color="getAfterSaleStatusColor(order.status)" class="status-badge">
                  {{ formatAfterSaleStatus(order) }}
                </q-badge>
              </div>
              <div class="text-caption text-grey-7 q-mt-xs">{{ formatDateTime(order.createTime) }}</div>
            </q-card-section>

            <q-separator />

            <!-- 商品信息 -->
            <q-card-section class="q-pa-sm">
              <div class="row no-wrap items-start">
                <!-- 商品图片 -->
                <div class="product-image-mobile q-mr-sm">
                  <q-img :src="order.picUrl || '/images/placeholder.png'" style="width: 60px; height: 60px" class="rounded-borders" fit="cover" spinner-color="primary" />
                </div>
                <!-- 商品信息 -->
                <div class="column justify-between flex-1">
                  <div class="text-body2 text-weight-medium q-mb-xs ellipsis-2-lines">{{ order.spuName }}</div>
                  <div class="text-caption text-grey-7 q-mb-xs">
                    {{ order.properties?.map((property) => property.valueName).join(' ') }}
                  </div>
                  <div class="row justify-between items-center">
                    <div class="text-body2 text-weight-medium text-primary">
                      {{ formatAmount(order.refundPrice) }}
                    </div>
                    <div class="text-caption text-grey-7">x{{ order.count }}</div>
                  </div>
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <!-- 售后信息和操作 -->
            <q-card-section class="q-pa-sm">
              <div class="row justify-between items-center q-mb-sm">
                <div class="text-body2 text-weight-medium">{{ formatAfterSaleWay(order.way) }}</div>
                <div class="text-caption text-grey-7">{{ formatAfterSaleStatusDescription(order) }}</div>
              </div>
              <div class="row justify-end q-gutter-xs">
                <q-btn v-if="order.buttons?.includes('cancel')" outline color="negative" size="xs" label="取消" @click.stop="onCancel(order.id)" />
                <q-btn v-if="order.buttons?.includes('delivery')" outline color="primary" size="xs" label="退货" @click.stop="onDelivery(order.id)" />
                <q-btn flat color="primary" size="xs" label="详情" @click.stop="goToDetail(order.id)" />
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- 分页 -->
        <div class="row justify-center q-mt-md">
          <q-pagination
            v-model="state.pagination.pageNo"
            :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
            :max-pages="$q.screen.lt.sm ? 3 : 6"
            boundary-links
            direction-links
            :dense="$q.screen.lt.sm"
            @update:model-value="onPageChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import AfterSaleApi from '~/composables/afterSaleApi';
import { useCurrency } from '~/composables/useCurrency';
import { formatAfterSaleStatus, formatAfterSaleStatusDescription, formatAfterSaleWay, getAfterSaleStatusColor, handleAfterSaleButtons } from '~/utils/afterSaleUtils';
import { formatDateTime } from '~/utils/dateUtil';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const { formatAmount } = useCurrency();

const state = reactive({
  currentTab: 'all',
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 10,
  },
  loadStatus: '',
});

// 切换选项卡
function onTabsChange(tab) {
  state.currentTab = tab;
  state.pagination.pageNo = 1;
  state.pagination.list = [];
  getOrderList();
}

// 获取售后列表
async function getOrderList() {
  state.loadStatus = 'loading';

  try {
    // 根据tab确定状态筛选
    let statusFilter = null;
    if (state.currentTab === 'processing') {
      statusFilter = [10, 20, 30, 40]; // 处理中状态
    } else if (state.currentTab === 'completed') {
      statusFilter = [50]; // 已完成状态
    } else if (state.currentTab === 'cancelled') {
      statusFilter = [61, 62, 63]; // 已取消状态
    }

    const { data, code } = await AfterSaleApi.getAfterSalePage({
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
      status: statusFilter,
    });

    if (code !== 0) {
      state.loadStatus = 'error';
      return;
    }

    // 处理按钮显示逻辑
    data.list.forEach((order) => handleAfterSaleButtons(order));

    if (state.pagination.pageNo === 1) {
      state.pagination.list = data.list;
    } else {
      state.pagination.list = [...state.pagination.list, ...data.list];
    }

    state.pagination.total = data.total;
    state.loadStatus = 'loaded';
  } catch (error) {
    console.error('获取售后列表失败:', error);
    state.loadStatus = 'error';
    $q.notify({
      color: 'negative',
      message: '获取售后列表失败',
      icon: 'error',
    });
  }
}

// 取消售后申请
function onCancel(orderId) {
  $q.dialog({
    title: '确认取消',
    message: '确定要取消此售后申请吗？',
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    try {
      const { code } = await AfterSaleApi.cancelAfterSale(orderId);
      if (code === 0) {
        $q.notify({
          color: 'positive',
          message: '取消成功',
          icon: 'check_circle',
        });
        // 重新加载列表
        state.pagination.pageNo = 1;
        state.pagination.list = [];
        await getOrderList();
      }
    } catch (error) {
      console.error('取消售后申请失败:', error);
      $q.notify({
        color: 'negative',
        message: '取消失败，请重试',
        icon: 'error',
      });
    }
  });
}

// 填写退货信息
function onDelivery(_orderId) {
  // TODO: 实现填写退货信息功能
  $q.notify({
    color: 'info',
    message: '填写退货信息功能开发中',
    icon: 'info',
  });
}

// 跳转到详情页
function goToDetail(orderId) {
  router.push(`/account/after-sale-detail?id=${orderId}`);
}

// 分页变化
function onPageChange(page) {
  state.pagination.pageNo = page;
  getOrderList();
}

onMounted(async () => {
  // 检查是否有类型参数
  if (route.query.type) {
    state.currentTab = route.query.type;
  }
  await getOrderList();
});
</script>

<style lang="scss" scoped>
.after-sale-list-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.after-sale-card {
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.after-sale-card-mobile {
  border: 1px solid #e0e0e0;
}

.product-image,
.product-image-mobile {
  flex-shrink: 0;
}

.after-sale-header {
  .after-sale-no {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .status-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
  }
}

.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  max-height: 2.8em;
}

// 移动端适配
@media (max-width: 599px) {
  .after-sale-list-page {
    .q-pa-md {
      padding: 12px;
    }
  }

  .mobile-after-sale-list {
    .after-sale-card-mobile {
      margin-bottom: 12px;
    }
  }
}
</style>
