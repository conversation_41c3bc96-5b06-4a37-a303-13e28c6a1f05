/**
 * 搜索工具函数
 * 用于检测用户输入类型、平台识别等功能
 */

// 支持的电商平台配置
export const PLATFORMS = {
  TAOBAO: 'taobao',
  TMALL: 'tmall', 
  ALIBABA_1688: '1688',
  JD: 'jd'
};

// 平台显示名称映射
export const PLATFORM_NAMES = {
  [PLATFORMS.TAOBAO]: '淘宝',
  [PLATFORMS.TMALL]: '天猫',
  [PLATFORMS.ALIBABA_1688]: '1688',
  [PLATFORMS.JD]: '京东'
};

// 平台URL模式匹配
const PLATFORM_PATTERNS = {
  [PLATFORMS.TAOBAO]: [
    /item\.taobao\.com/i,
    /world\.taobao\.com/i
  ],
  [PLATFORMS.TMALL]: [
    /detail\.tmall\.com/i,
    /chaoshi\.tmall\.com/i
  ],
  [PLATFORMS.ALIBABA_1688]: [
    /detail\.1688\.com/i,
    /offer\.1688\.com/i
  ],
  [PLATFORMS.JD]: [
    /item\.jd\.com/i,
    /item\.m\.jd\.com/i
  ]
};

/**
 * 检测输入是否为URL
 * @param {string} input - 用户输入
 * @returns {boolean} 是否为URL
 */
export function isUrl(input) {
  if (!input || typeof input !== 'string') {
    return false;
  }
  
  // 简单的URL检测正则
  const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
  
  // 检测是否包含常见的URL特征
  const hasUrlFeatures = input.includes('.') && 
    (input.includes('http') || input.includes('www') || input.includes('.com') || input.includes('.cn'));
  
  return urlPattern.test(input) || hasUrlFeatures;
}

/**
 * 识别商品链接所属平台
 * @param {string} url - 商品链接
 * @returns {string|null} 平台代码，如果无法识别则返回null
 */
export function detectPlatform(url) {
  if (!url || typeof url !== 'string') {
    return null;
  }
  
  // 遍历所有平台模式进行匹配
  for (const [platform, patterns] of Object.entries(PLATFORM_PATTERNS)) {
    for (const pattern of patterns) {
      if (pattern.test(url)) {
        return platform;
      }
    }
  }
  
  return null;
}

/**
 * 获取平台显示名称
 * @param {string} platform - 平台代码
 * @returns {string} 平台显示名称
 */
export function getPlatformName(platform) {
  return PLATFORM_NAMES[platform] || platform;
}

/**
 * 分析用户搜索输入
 * @param {string} input - 用户输入
 * @returns {Object} 分析结果
 */
export function analyzeSearchInput(input) {
  if (!input || typeof input !== 'string') {
    return {
      type: 'invalid',
      input: input,
      platform: null,
      isUrl: false
    };
  }
  
  const trimmedInput = input.trim();
  const isUrlInput = isUrl(trimmedInput);
  
  if (isUrlInput) {
    const platform = detectPlatform(trimmedInput);
    return {
      type: 'url',
      input: trimmedInput,
      platform: platform,
      platformName: platform ? getPlatformName(platform) : null,
      isUrl: true
    };
  } else {
    return {
      type: 'keyword',
      input: trimmedInput,
      platform: null,
      isUrl: false
    };
  }
}

/**
 * 构建搜索URL
 * @param {Object} searchData - 搜索数据
 * @param {string} searchData.type - 搜索类型 ('keyword' | 'url')
 * @param {string} searchData.input - 用户输入
 * @param {string} searchData.platform - 平台代码
 * @param {number} page - 页码（可选，默认为1）
 * @returns {string} 搜索URL
 */
export function buildSearchUrl(searchData, page = 1) {
  if (!searchData || !searchData.input) {
    return '/search';
  }
  
  if (searchData.type === 'url') {
    // 链接搜索跳转到商品详情页
    return `/product/detail?url=${encodeURIComponent(searchData.input)}`;
  } else {
    // 关键字搜索跳转到搜索结果页
    const params = new URLSearchParams();
    params.set('q', searchData.input);
    params.set('platform', searchData.platform || PLATFORMS.TAOBAO);
    if (page > 1) {
      params.set('page', page.toString());
    }
    return `/search?${params.toString()}`;
  }
}

/**
 * 验证商品链接格式
 * @param {string} url - 商品链接
 * @returns {Object} 验证结果
 */
export function validateProductUrl(url) {
  const analysis = analyzeSearchInput(url);
  
  if (analysis.type !== 'url') {
    return {
      valid: false,
      error: '请输入有效的商品链接'
    };
  }
  
  if (!analysis.platform) {
    return {
      valid: false,
      error: '暂不支持该平台的商品链接'
    };
  }
  
  return {
    valid: true,
    platform: analysis.platform,
    platformName: analysis.platformName
  };
}
