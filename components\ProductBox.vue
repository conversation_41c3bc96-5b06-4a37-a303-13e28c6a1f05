<template>
  <div class="product-card">
    <nuxt-link :to="{ path: '/product/' + product.id + '/' + generateSlug(product.name || product.title) }">
      <div class="image-container">
        <q-skeleton v-if="!product" class="product-skeleton" :animated="true" />
        <img
          v-else
          :src="getThumbnailUrl(product.picUrl || (product.images && product.images.length > 0 ? product.images[0].src : ''), '400x400')"
          :alt="product.name || product.title"
          class="product-image" />

        <!-- 折扣标签 -->
        <div v-if="product.discount" class="discount-badge">-{{ product.discount }}%</div>
      </div>

      <div class="product-info">
        <q-skeleton v-if="!product" :width="'100%'" :height="'40px'" :animated="true" />
        <p class="product-title">{{ product.name || product.title }}</p>

        <!-- 商品来源 -->
        <p v-if="product.source || product.shopName" class="product-source">
          <q-icon name="store" size="14px" class="q-mr-xs" />
          {{ product.source || product.shopName }}
        </p>

        <q-skeleton v-if="!product" :width="'60%'" :height="'20px'" :animated="true" />
        <div class="product-price-container">
          <p class="product-price">
            ¥ {{ fen2yuan(product.price) }}
            <span class="currency-price">{{ price2Currency(product.price, currency) }}</span>
          </p>

          <!-- 市场价 -->
          <p v-if="product.marketPrice && product.marketPrice > product.price" class="product-market-price">¥ {{ fen2yuan(product.marketPrice) }}</p>
        </div>
      </div>
    </nuxt-link>

    <!-- 快速操作按钮 -->
    <div class="product-actions">
      <q-btn flat round size="sm" icon="favorite_border" color="grey-7" @click.stop="addToWishlist" />
      <q-btn flat round size="sm" icon="shopping_cart" color="primary" @click.stop="addToCart" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useCurrencyStore } from '~/store/currency';
import { useWishlistStore } from '~/store/wishlist';
import { useCartStore } from '~/store/cart';
import { useNuxtApp } from '#app';

const nuxtApp = useNuxtApp();
const currencyStore = useCurrencyStore();
const wishlistStore = useWishlistStore();
const cartStore = useCartStore();

const currency = computed(() => {
  return currencyStore.selectedCurrency;
});

// Props
const props = defineProps({
  product: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
});

// 添加到收藏夹
function addToWishlist() {
  try {
    wishlistStore.addToWishlist({
      id: 0,
      spu: {
        id: props.product.id,
        name: props.product.name || props.product.title,
        picUrl: props.product.picUrl || (props.product.images && props.product.images.length > 0 ? props.product.images[0].src : ''),
        categoryId: props.product.categoryId || props.product.category,
        price: props.product.price,
      },
    });
    nuxtApp.$showNotify({ msg: nuxtApp.$i18n.t('notify.addedToWishlist'), type: 'positive' });
  } catch (error) {
    console.error('添加到收藏夹失败:', error);
    nuxtApp.$showNotify({ msg: nuxtApp.$i18n.t('notify.addToWishlistFailed'), type: 'negative' });
  }
}

// 添加到购物车
function addToCart() {
  try {
    cartStore.addToCart({
      spuId: props.product.id,
      count: 1,
      skuId: props.product.skuId || (props.product.skus && props.product.skus.length > 0 ? props.product.skus[0].id : null),
    });
    nuxtApp.$showNotify({ msg: nuxtApp.$i18n.t('notify.addedToCart'), type: 'positive' });
  } catch (error) {
    console.error('添加到购物车失败:', error);
    nuxtApp.$showNotify({ msg: nuxtApp.$i18n.t('notify.addToCartFailed'), type: 'negative' });
  }
}
</script>

<style lang="scss" scoped>
.product-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  text-align: left;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);

    .product-actions {
      opacity: 1;
    }
  }

  .image-container {
    position: relative;
    width: 100%;
    padding-bottom: 100%; /* 创建1:1的宽高比（方形） */
    overflow: hidden;
  }

  .product-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain; /* 确保图片完整显示且不变形 */
    background-color: #f9f9f9; /* 图片背景色 */
    transition: transform 0.5s ease;
    transform-origin: center;
    padding: 10px;

    &:hover {
      transform: scale(1.05);
    }
  }

  .discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #e53935;
    color: white;
    font-size: 12px;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
    z-index: 1;
  }

  .product-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
  }

  .product-info {
    padding: 12px 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    @media (max-width: 599px) {
      padding: 10px 12px 15px; /* 增加底部内边距，确保价格完全显示 */
    }

    .product-title {
      display: -webkit-box;
      -webkit-line-clamp: 2; /* 减少为2行，避免内容溢出 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.5;
      max-height: 3em;
      font-size: 16px;
      color: #333;
      margin: 0 0 8px;
      font-weight: 500;

      @media (max-width: 599px) {
        font-size: 14px;
        margin: 0 0 6px;
      }
    }

    .product-source {
      font-size: 12px;
      color: #666;
      margin: 0 0 10px;
      display: flex;
      align-items: center;
    }

    .product-price-container {
      display: flex;
      align-items: baseline;
      margin-top: auto;
    }

    .product-price {
      font-size: 18px;
      color: #e53935;
      font-weight: bold;
      margin: 0;

      .currency-price {
        font-size: 13px;
        color: #999;
        font-weight: normal;
        margin-left: 5px;
      }

      @media (max-width: 599px) {
        font-size: 16px;
      }
    }

    .product-market-price {
      font-size: 13px;
      color: #999;
      text-decoration: line-through;
      margin: 0 0 0 8px;
    }
  }

  .product-actions {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;

    @media (max-width: 599px) {
      opacity: 1;
      flex-direction: row;
      top: auto;
      bottom: 10px;
      left: 10px;
    }

    .q-btn {
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

      &:hover {
        background-color: white;
      }
    }
  }
}
</style>
