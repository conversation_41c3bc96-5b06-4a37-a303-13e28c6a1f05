<template>
  <div>
    <!-- PC视图商品表格 -->
    <q-table class="desktop-table" :rows="orderInfo.items" :columns="columns" row-key="id" hide-pagination hide-bottom flat bordered :rows-per-page-options="[0]">
      <template #header="slotProps">
        <q-tr :props="slotProps">
          <q-th auto-width class="text-center">{{ $t('accountOrderDetail.summary.item') }}</q-th>
          <q-th auto-width class="text-center">{{ $t('accountOrderDetail.summary.price') }}</q-th>
          <q-th auto-width class="text-center">{{ $t('accountOrderDetail.summary.quantity') }}</q-th>
          <q-th auto-width class="text-center">{{ $t('accountOrderDetail.summary.subtotal') }}</q-th>
        </q-tr>
      </template>

      <template #body="slotProps">
        <q-tr :props="slotProps">
          <q-td key="product" :props="slotProps">
            <div class="row no-wrap items-start q-gutter-x-md product-cell">
              <q-img :src="getThumbnailUrl(slotProps.row.picUrl, '80x80')" style="width: 70px; height: 70px" class="rounded-borders q-mr-sm" />
              <div class="col">
                <div class="text-weight-medium text-body2 product-name">{{ slotProps.row.spuName }}</div>
                <div class="text-grey-7 text-caption q-mt-xs">{{ formattedProperties(slotProps.row.properties) }}</div>
              </div>
            </div>
          </q-td>
          <q-td key="price" :props="slotProps" class="text-center">
            <div v-html="formatAmount(slotProps.row.price)"></div>
          </q-td>
          <q-td key="quantity" :props="slotProps" class="text-center">{{ slotProps.row.count }}</q-td>
          <q-td key="subtotal" :props="slotProps" class="text-center text-primary text-weight-medium">
            <div v-html="formatAmount(slotProps.row.price * slotProps.row.count)"></div>
          </q-td>
        </q-tr>
      </template>
    </q-table>

    <!-- 移动端商品卡片 -->
    <div class="mobile-product-list q-gutter-y-sm">
      <q-card v-for="item in orderInfo.items" :key="item.id" flat bordered class="q-pa-sm">
        <div class="row no-wrap items-start q-gutter-x-md">
          <q-img :src="getThumbnailUrl(item.picUrl, '80x80')" style="width: 70px; height: 70px" class="rounded-borders" />
          <div class="col">
            <div class="text-weight-medium text-body2 product-name">{{ item.spuName }}</div>
            <div class="text-grey-7 text-caption q-mt-xs">{{ formattedProperties(item.properties) }}</div>
          </div>
        </div>

        <q-separator class="q-my-sm" />

        <div class="row justify-between q-py-xs">
          <div class="text-body2">
            <span class="text-grey-7">{{ $t('accountOrderDetail.summary.unitPrice') }}</span>
            <span class="text-primary" v-html="formatAmount(item.price)"></span>
          </div>
          <div class="text-body2">
            <span class="text-grey-7">{{ $t('accountOrderDetail.summary.quantityLabel') }}</span>
            <span class="text-weight-medium">{{ item.count }}</span>
          </div>
        </div>

        <q-separator class="q-my-xs" spaced inset />

        <div class="row justify-end q-py-xs">
          <div class="text-body2">
            <span class="text-grey-7">{{ $t('accountOrderDetail.summary.subtotalLabel') }}</span>
            <span class="text-primary text-weight-medium" v-html="formatAmount(item.price * item.count)"></span>
          </div>
        </div>
      </q-card>
    </div>

    <!-- 费用摘要 -->
    <q-card flat bordered class="q-mt-md">
      <q-card-section class="q-pa-sm">
        <div class="row justify-end">
          <div class="col-12 col-sm-8 col-md-6">
            <q-list dense>
              <q-item>
                <q-item-section>{{ $t('accountOrderDetail.price.totalProducts') }}</q-item-section>
                <q-item-section side>
                  <div v-html="formatAmount(orderInfo.totalPrice)"></div>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>{{ $t('accountOrderDetail.price.shipping') }}</q-item-section>
                <q-item-section side>
                  <div v-html="formatAmount(orderInfo.deliveryPrice)"></div>
                </q-item-section>
              </q-item>

              <q-item v-if="orderInfo.discountPrice > 0">
                <q-item-section>{{ $t('accountOrderDetail.price.discount') }}</q-item-section>
                <q-item-section side class="text-negative"><span v-html="formatAmount(orderInfo.discountPrice).replace(/<[^>]*>/g, '')"></span> </q-item-section>
              </q-item>
              <q-item v-if="orderInfo.servicePrice > 0">
                <q-item-section>{{ $t('accountOrderDetail.price.serviceFee') }}</q-item-section>
                <q-item-section side class="text-negative"><span v-html="formatAmount(orderInfo.servicePrice).replace(/<[^>]*>/g, '')"></span> </q-item-section>
              </q-item>
              <q-item v-if="orderInfo.platformPrice > 0">
                <q-item-section>{{ $t('accountOrderDetail.price.platformFee') }}</q-item-section>
                <q-item-section side class="text-negative"><span v-html="formatAmount(orderInfo.platformPrice).replace(/<[^>]*>/g, '')"></span> </q-item-section>
              </q-item>

              <q-separator spaced inset />

              <q-item>
                <q-item-section class="text-weight-medium text-body1">{{ $t('accountOrderDetail.price.actualPayment') }}</q-item-section>
                <q-item-section side class="text-primary text-weight-medium text-body1">
                  <div v-html="formatAmount(orderInfo.payPrice)"></div>
                </q-item-section>
              </q-item>
              <q-item v-if="orderInfo.hasSupplement">
                <q-item-section class="text-weight-medium text-body1" v-if="orderInfo.supplementStatus === 0">待补款金额：</q-item-section>
                <q-item-section class="text-weight-medium text-body1" v-if="orderInfo.supplementStatus === 1">补款金额：</q-item-section>
                <q-item-section side class="text-primary text-weight-medium text-body1">
                  <div v-html="formatAmount(orderInfo.supplementPrice)"></div>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { useCurrency } from '~/composables/useCurrency';
import { useI18n } from 'vue-i18n';

defineProps({
  orderInfo: {
    type: Object,
    required: true,
  },
  // items: {
  //   type: Array,
  //   required: true,
  // },
  // totalPrice: {
  //   type: Number,
  //   required: true,
  // },
  // deliveryPrice: {
  //   type: Number,
  //   required: true,
  // },
  // discountPrice: {
  //   type: Number,
  //   required: true,
  // },
  // servicePrice: {
  //   type: Number,
  //   required: true,
  // },
  // platformPrice: {
  //   type: Number,
  //   required: true,
  // },
  // payPrice: {
  //   type: Number,
  //   required: true,
  // },
});

const { formatAmount } = useCurrency();
const { t } = useI18n();

// 定义表格列
const columns = [
  {
    name: 'product',
    label: t('accountOrderDetail.summary.item'),
    field: 'spuName',
    align: 'center',
    style: 'width: 50%; max-width: 50%',
  },
  {
    name: 'price',
    label: t('accountOrderDetail.summary.price'),
    field: 'price',
    align: 'center',
  },
  {
    name: 'quantity',
    label: t('accountOrderDetail.summary.quantity'),
    field: 'count',
    align: 'center',
  },
  {
    name: 'subtotal',
    label: t('accountOrderDetail.summary.subtotal'),
    field: (row) => row.price * row.count,
    align: 'center',
  },
];
</script>

<style lang="scss" scoped>
/* 自定义样式 */
.desktop-table {
  display: block;

  @media (max-width: 599px) {
    display: none;
  }
}

.mobile-product-list {
  display: none;

  @media (max-width: 599px) {
    display: block;
  }
}

/* 文本截断样式 */
.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

/* 商品名称样式 */
.product-name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  text-align: left;
  white-space: normal;
  max-width: 100%;
}

/* 商品单元格样式 */
.product-cell {
  width: 100%;
  max-width: 100%;
}
</style>
