<template>
  <Header />
  <div class="news-detail-page">
    <div class="news-container">
      <!-- 新闻内容 -->
      <div v-if="news" class="news-content-wrapper">
        <!-- 新闻标题和元信息 -->
        <div class="news-header">
          <h1 class="news-title">{{ news.title }}</h1>
          <div class="news-meta">
            <div class="meta-item">
              <q-icon name="event" size="sm" class="q-mr-xs" />
              <span>{{ formatDate(news.publishDate) }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="visibility" size="sm" class="q-mr-xs" />
              <span>{{ news.views }} 阅读</span>
            </div>
            <div class="meta-item">
              <q-badge :color="getCategoryColor(news.category)">
                {{ getCategoryText(news.category) }}
              </q-badge>
            </div>
          </div>
        </div>

        <!-- 新闻内容 -->
        <div class="news-content q-mt-lg">
          <div v-html="news.content"></div>
        </div>

        <!-- 附件（如果有） -->
        <div v-if="news.attachments && news.attachments.length > 0" class="news-attachments q-mt-lg">
          <q-separator class="q-mb-md" />
          <h3 class="subsection-title">附件下载</h3>
          <q-list bordered separator>
            <q-item v-for="(attachment, index) in news.attachments" :key="index" clickable @click="downloadAttachment(attachment)">
              <q-item-section avatar>
                <q-icon :name="getAttachmentIcon(attachment.type)" color="primary" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ attachment.name }}</q-item-label>
                <q-item-label caption>{{ formatFileSize(attachment.size) }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-btn flat round color="primary" icon="download" />
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- 分享和返回 -->
        <div class="news-actions q-mt-lg">
          <div class="row justify-between items-center">
            <div class="social-share">
              <q-btn flat round color="primary" icon="share" class="q-mr-sm">
                <q-tooltip>分享公告</q-tooltip>
              </q-btn>
            </div>
            <q-btn flat color="primary" to="/news" class="back-btn">
              <q-icon name="arrow_back" class="q-mr-xs" />
              返回列表
            </q-btn>
          </div>
        </div>
      </div>

      <!-- 相关公告 -->
      <div v-if="relatedNews.length > 0" class="related-news q-mt-xl">
        <h2 class="section-title">相关公告</h2>
        <q-list bordered separator>
          <q-item v-for="item in relatedNews" :key="item.id" :to="`/news/${item.id}`" clickable v-ripple class="news-item">
            <q-item-section avatar>
              <q-icon :name="getCategoryIcon(item.category)" :color="getCategoryColor(item.category)" size="md" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="news-title">
                {{ item.title }}
              </q-item-label>
              <q-item-label caption lines="1" class="news-summary">
                {{ item.summary }}
              </q-item-label>
            </q-item-section>

            <q-item-section side>
              <div class="column items-end">
                <q-item-label caption>{{ formatDate(item.publishDate) }}</q-item-label>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- 加载中状态 -->
      <div v-else-if="loading" class="loading-state">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-state">
        <q-icon name="error_outline" color="negative" size="3em" />
        <div class="q-mt-md">公告不存在或已被删除</div>
        <q-btn color="primary" to="/news" class="q-mt-md"> 返回公告列表 </q-btn>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { date } from 'quasar';

const route = useRoute();
const newsId = computed(() => parseInt(route.params.id));

// 状态
const loading = ref(true);
const news = ref(null);

// 模拟新闻数据
const newsData = [
  {
    id: 1,
    title: '系统维护通知：平台将于6月15日凌晨2点至6点进行系统升级',
    summary: '为提升用户体验，我们将于6月15日凌晨2点至6点进行系统升级维护，期间平台将暂停服务，给您带来的不便敬请谅解。',
    publishDate: '2023-06-10',
    category: 'notice',
    isPinned: true,
    views: 5678,
    content: `<p>尊敬的用户：</p>
    <p>为了提升系统性能和用户体验，我们计划于2023年6月15日凌晨2:00至6:00进行系统升级维护。在此期间，平台的所有服务（包括网站和移动应用）将暂时无法访问。</p>

    <h3>维护内容</h3>
    <ol>
      <li>系统架构升级，提高平台稳定性和响应速度</li>
      <li>数据库优化，提升数据处理效率</li>
      <li>安全系统更新，增强用户数据保护</li>
      <li>修复已知问题，优化用户体验</li>
    </ol>

    <h3>注意事项</h3>
    <ul>
      <li>请您在维护前完成正在进行的操作，以免造成数据丢失</li>
      <li>维护期间产生的订单将在系统恢复后按照正常流程处理</li>
      <li>如有紧急问题，请通过客服热线联系我们</li>
    </ul>

    <p>系统维护结束后，您可能需要清除浏览器缓存或重新登录才能正常访问平台。</p>

    <p>感谢您的理解和支持，我们将尽量缩短维护时间，为您提供更好的服务体验。</p>

    <p>如有任何疑问，请联系我们的客服团队。</p>`,
    attachments: [
      {
        name: '系统维护详细说明.pdf',
        type: 'pdf',
        size: 2048576,
        url: '#',
      },
      {
        name: '常见问题解答.docx',
        type: 'doc',
        size: 1048576,
        url: '#',
      },
    ],
  },
  {
    id: 3,
    title: '关于调整国际物流费用的通知',
    summary: '受国际油价上涨和运力紧张影响，我们将从7月1日起调整部分国家和地区的国际物流费用，详情请查看公告。',
    publishDate: '2023-06-25',
    category: 'policy',
    isPinned: true,
    views: 4567,
    content: `<p>尊敬的用户：</p>
    <p>受国际油价持续上涨、全球运力紧张以及各国疫情防控政策调整等因素影响，国际物流成本显著增加。经综合评估，我们将从2023年7月1日起调整部分国家和地区的国际物流费用。</p>

    <h3>调整内容</h3>
    <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse;">
      <tr>
        <th>地区</th>
        <th>原运费（元/kg）</th>
        <th>调整后运费（元/kg）</th>
        <th>涨幅</th>
      </tr>
      <tr>
        <td>北美地区</td>
        <td>80</td>
        <td>88</td>
        <td>10%</td>
      </tr>
      <tr>
        <td>欧洲地区</td>
        <td>90</td>
        <td>99</td>
        <td>10%</td>
      </tr>
      <tr>
        <td>澳洲地区</td>
        <td>85</td>
        <td>93.5</td>
        <td>10%</td>
      </tr>
      <tr>
        <td>东南亚地区</td>
        <td>60</td>
        <td>63</td>
        <td>5%</td>
      </tr>
      <tr>
        <td>日韩地区</td>
        <td>70</td>
        <td>73.5</td>
        <td>5%</td>
      </tr>
    </table>

    <h3>其他说明</h3>
    <ul>
      <li>上述费用调整仅适用于2023年7月1日（含）之后创建的订单</li>
      <li>7月1日前已创建的订单，将按照原费率计算运费</li>
      <li>我们将持续关注国际物流市场变化，适时调整物流费用</li>
      <li>为减轻用户负担，我们已对部分热门商品提供运费补贴</li>
    </ul>

    <p>我们理解此次调整可能会给您带来一定影响，但这是确保服务质量的必要措施。我们将继续优化物流网络，提高运营效率，为您提供更优质的服务。</p>

    <p>感谢您的理解和支持！</p>`,
    attachments: [
      {
        name: '国际物流费用调整详情.xlsx',
        type: 'excel',
        size: 1572864,
        url: '#',
      },
    ],
  },
  {
    id: 5,
    title: '关于防范网络诈骗的重要提醒',
    summary: '近期有不法分子冒充平台客服实施诈骗，请广大用户提高警惕，保护个人信息和财产安全。',
    publishDate: '2023-07-05',
    category: 'notice',
    isPinned: true,
    views: 7890,
    content: `<p>尊敬的用户：</p>
    <p>近期，我们接到多起用户反馈，有不法分子冒充平台客服人员，通过电话、短信、社交媒体等渠道实施诈骗。为保障您的账户和财产安全，特发布此安全提醒。</p>

    <h3>常见诈骗手段</h3>
    <ol>
      <li><strong>冒充客服诈骗</strong>：不法分子冒充平台客服，以订单异常、账户安全等为由，诱导用户提供账号密码、验证码或进行转账操作。</li>
      <li><strong>钓鱼网站诈骗</strong>：不法分子搭建与平台相似的钓鱼网站，诱导用户登录，窃取账号密码。</li>
      <li><strong>虚假活动诈骗</strong>：不法分子发布虚假的优惠活动信息，诱导用户点击不明链接或支付费用。</li>
      <li><strong>退款诈骗</strong>：不法分子谎称用户的订单出现问题需要退款，诱导用户提供银行卡信息或进行转账操作。</li>
    </ol>

    <h3>防范措施</h3>
    <ul>
      <li>我们的客服人员<strong>绝不会</strong>以任何理由要求您提供账号密码、银行卡密码、短信验证码等敏感信息。</li>
      <li>我们的客服人员<strong>绝不会</strong>要求您通过微信、支付宝等方式向陌生账户转账。</li>
      <li>请通过官方网站、官方APP或官方客服热线（400-888-9999）联系我们。</li>
      <li>接到可疑电话或信息时，请先通过官方渠道核实。</li>
      <li>定期修改账号密码，开启双因素认证，提高账号安全性。</li>
    </ul>

    <h3>如何识别官方联系方式</h3>
    <p>我们的官方联系方式如下：</p>
    <ul>
      <li>官方网站：www.example.com</li>
      <li>客服热线：400-888-9999（工作时间：9:00-22:00）</li>
      <li>官方邮箱：<EMAIL></li>
      <li>官方微信公众号：ExampleOfficial</li>
    </ul>

    <p>如果您遭遇诈骗或发现可疑情况，请立即联系我们的客服团队，并向当地公安机关报案。</p>

    <p>我们将持续加强平台安全建设，保障用户的信息和财产安全。感谢您的配合和支持！</p>`,
    attachments: [
      {
        name: '防范电信网络诈骗指南.pdf',
        type: 'pdf',
        size: 3145728,
        url: '#',
      },
    ],
  },
];

// 获取新闻详情
onMounted(async () => {
  try {
    loading.value = true;

    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 在实际应用中，这里应该是API调用
    // const response = await fetch(`/api/news/${newsId.value}`);
    // const data = await response.json();
    // news.value = data;

    // 使用模拟数据
    const foundNews = newsData.find((item) => item.id === newsId.value);
    if (foundNews) {
      news.value = foundNews;
    }

    loading.value = false;
  } catch (error) {
    console.error('Failed to fetch news details:', error);
    loading.value = false;
  }
});

// 相关新闻（根据分类匹配）
const relatedNews = computed(() => {
  if (!news.value) return [];

  // 找出与当前新闻分类相同的其他新闻
  return newsData
    .filter((item) => item.id !== newsId.value) // 排除当前新闻
    .filter((item) => item.category === news.value.category) // 筛选相同分类
    .slice(0, 3); // 最多显示3条相关新闻
});

// 格式化日期
const formatDate = (dateString) => {
  return date.formatDate(new Date(dateString), 'YYYY-MM-DD');
};

// 获取分类图标
const getCategoryIcon = (category) => {
  switch (category) {
    case 'notice':
      return 'announcement';
    case 'update':
      return 'update';
    case 'policy':
      return 'policy';
    case 'promotion':
      return 'campaign';
    default:
      return 'article';
  }
};

// 获取分类颜色
const getCategoryColor = (category) => {
  switch (category) {
    case 'notice':
      return 'red';
    case 'update':
      return 'green';
    case 'policy':
      return 'blue';
    case 'promotion':
      return 'orange';
    default:
      return 'grey';
  }
};

// 获取分类文本
const getCategoryText = (category) => {
  switch (category) {
    case 'notice':
      return '系统公告';
    case 'update':
      return '平台更新';
    case 'policy':
      return '政策变动';
    case 'promotion':
      return '活动预告';
    default:
      return '其他';
  }
};

// 获取附件图标
const getAttachmentIcon = (type) => {
  switch (type) {
    case 'pdf':
      return 'picture_as_pdf';
    case 'doc':
    case 'docx':
      return 'description';
    case 'xls':
    case 'xlsx':
    case 'excel':
      return 'table_chart';
    case 'ppt':
    case 'pptx':
      return 'slideshow';
    case 'zip':
    case 'rar':
      return 'folder_zip';
    case 'img':
    case 'jpg':
    case 'png':
      return 'image';
    default:
      return 'insert_drive_file';
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes < 1024) {
    return bytes + ' B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + ' KB';
  } else if (bytes < 1024 * 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 下载附件
const downloadAttachment = (attachment) => {
  // 在实际应用中，这里应该是下载文件的逻辑
  console.log('Downloading attachment:', attachment.name);
  // window.open(attachment.url, '_blank');

  // 模拟下载成功提示
  alert(`正在下载: ${attachment.name}`);
};
</script>

<style lang="scss" scoped>
.news-detail-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.news-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 16px;
}

.news-content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.news-header {
  margin-bottom: 20px;
}

.news-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  line-height: 1.3;
}

.news-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  color: #666;
  font-size: 14px;

  .meta-item {
    display: flex;
    align-items: center;
  }
}

.news-content {
  font-size: 16px;
  line-height: 1.7;
  color: #333;

  :deep(h3) {
    font-size: 20px;
    font-weight: bold;
    margin: 24px 0 16px;
    color: #1976d2;
  }

  :deep(p) {
    margin-bottom: 16px;
  }

  :deep(ul),
  :deep(ol) {
    margin-bottom: 16px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
    }
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;

    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 16px 0;
  }

  :deep(a) {
    color: #1976d2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.subsection-title {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 16px;
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;

  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }
}

.news-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.social-share {
  display: flex;
  align-items: center;
}

.back-btn {
  font-size: 14px;
}

.related-news {
  margin-top: 40px;
}

.news-item {
  &:hover {
    background-color: #f5f5f5;
  }
}

.news-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
}

.news-summary {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 4px;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 767px) {
  .news-content-wrapper {
    padding: 16px;
  }

  .news-title {
    font-size: 22px;
    margin-bottom: 12px;
  }

  .news-meta {
    font-size: 13px;
    gap: 12px;
  }

  .news-content {
    font-size: 15px;

    :deep(h3) {
      font-size: 18px;
      margin: 20px 0 12px;
    }
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .subsection-title {
    font-size: 16px;
  }
}
</style>
