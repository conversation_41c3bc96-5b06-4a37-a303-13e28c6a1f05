<template>
  <div>
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <CookieConsent />
  </div>
</template>
<script setup>
import { useCurrencyStore } from '~/store/currency';
import { useNoticeStore } from './store/notice';

const currencyStore = useCurrencyStore();
const noticeStore = useNoticeStore();

// 初始化货币设置
onMounted(() => {
  // 从cookie中获取货币设置
  const cookieCurrency = getCookie('currency');
  if (cookieCurrency) {
    try {
      const currencyObj = JSON.parse(cookieCurrency);
      currencyStore.setCurrentCurrency(currencyObj);
    } catch (e) {
      console.error('解析cookie中的货币设置失败:', e);
    }
  }
});

// 获取cookie值的辅助函数
function getCookie(name) {
  if (process.client) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
  }
  return null;
}

// 加载数据
currencyStore.fetchCurrencyList();
noticeStore.fetchNoticeList();
</script>
