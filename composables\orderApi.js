import { isEmpty } from '~/utils/utils';

const OrderApi = {
  // 计算订单信息
  settlementOrder: (data) => {
    const data2 = {
      ...data,
    };
    // 移除多余字段
    if (!(data.couponId > 0)) {
      delete data2.couponId;
    }
    if (!(data.addressId > 0)) {
      delete data2.addressId;
    }
    if (!(data.pickUpStoreId > 0)) {
      delete data2.pickUpStoreId;
    }
    if (isEmpty(data.receiverName)) {
      delete data2.receiverName;
    }
    if (isEmpty(data.receiverMobile)) {
      delete data2.receiverMobile;
    }
    if (!(data.combinationActivityId > 0)) {
      delete data2.combinationActivityId;
    }
    if (!(data.combinationHeadId > 0)) {
      delete data2.combinationHeadId;
    }
    if (!(data.seckillActivityId > 0)) {
      delete data2.seckillActivityId;
    }
    if (!(data.pointActivityId > 0)) {
      delete data2.pointActivityId;
    }

    // 解决 SpringMVC 接受 List<Item> 参数的问题
    delete data2.items;
    for (let i = 0; i < data.items.length; i++) {
      data2[encodeURIComponent('items[' + i + '].skuId')] = data.items[i].skuId + '';
      data2[encodeURIComponent('items[' + i + '].count')] = data.items[i].count + '';
      if (data.items[i].cartId) {
        data2[encodeURIComponent('items[' + i + '].cartId')] = data.items[i].cartId + '';
      }

      // 添加商品项的免费服务
      if (data.items[i].freeServices && data.items[i].freeServices.length > 0) {
        for (let j = 0; j < data.items[i].freeServices.length; j++) {
          data2[encodeURIComponent('items[' + i + '].freeServices[' + j + ']')] = data.items[i].freeServices[j].id + '';
        }
      }

      // 添加商品项的收费服务
      if (data.items[i].chargeServices && data.items[i].chargeServices.length > 0) {
        for (let j = 0; j < data.items[i].chargeServices.length; j++) {
          data2[encodeURIComponent('items[' + i + '].chargeServices[' + j + ']')] = data.items[i].chargeServices[j].id + '';
        }
      }
    }

    // 添加整个订单的免费服务
    delete data2.freeServices;
    if (data.freeServices && data.freeServices.length > 0) {
      for (let i = 0; i < data.freeServices.length; i++) {
        data2[encodeURIComponent('freeServices[' + i + ']')] = data.freeServices[i].id + '';
      }
    }

    // 添加整个订单的收费服务
    delete data2.chargeServices;
    if (data.chargeServices && data.chargeServices.length > 0) {
      for (let i = 0; i < data.chargeServices.length; i++) {
        data2[encodeURIComponent('chargeServices[' + i + ']')] = data.chargeServices[i].id + '';
      }
    }

    const queryString = Object.keys(data2)
      .map((key) => key + '=' + data2[key])
      .join('&');
    return useClientGet(`/trade/order/settlement?${queryString}`, {
      custom: {
        showError: true,
        showLoading: false,
      },
    });
  },

  // 获得商品结算信息
  getSettlementProduct: (spuIds) => {
    return useClientGet('/trade/order/settlement-product', {
      params: { spuIds },
      custom: {
        showLoading: false,
        showError: false,
      },
    });
  },
  // 创建订单
  createOrder: (data) => {
    return useClientPost(`/trade/order/create`, {
      body: data,
      custom: {
        showError: true,
      },
    });
  },
  // 获得订单详细：sync 是可选参数
  getOrderDetail: (id, sync) => {
    return useClientGet(`/trade/order/get-detail`, {
      params: {
        id,
        sync,
      },
      custom: {
        showLoading: false,
      },
    });
  },
  // 订单列表
  getOrderPage: (params) => {
    return useClientGet('/trade/order/page', {
      params,
      custom: {
        showLoading: false,
      },
    });
  },
  // 确认收货
  receiveOrder: (id) => {
    return useClientPut(`/trade/order/receive`, {
      params: {
        id,
      },
    });
  },
  // 取消订单
  cancelOrder: (id) => {
    return useClientDelete(`/trade/order/cancel`, {
      params: {
        id,
      },
    });
  },
  // 删除订单
  deleteOrder: (id) => {
    return useClientDelete(`/trade/order/delete`, {
      params: {
        id,
      },
    });
  },
  // 获得交易订单的物流轨迹
  getOrderExpressTrackList: (id) => {
    return useClientGet(`/trade/order/get-express-track-list`, {
      params: {
        id,
      },
    });
  },
  // 获得交易订单数量
  getOrderCount: () => {
    return useClientGet('/trade/order/get-count', {
      custom: {
        showLoading: false,
        auth: true,
      },
    });
  },
  // 创建单个评论
  createOrderItemComment: (data) => {
    return useClientPost(`/trade/order/item/create-comment`, {
      data,
    });
  },
};

export default OrderApi;
