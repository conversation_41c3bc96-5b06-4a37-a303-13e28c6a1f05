<template>
  <div class="maintenance-page">
    <div class="maintenance-container">
      <div class="logo-container">
        <img src="/logo.png" alt="Logo" class="logo" />
      </div>
      
      <h1 class="title">系统维护中</h1>
      <p class="description">为了提供更好的服务体验，我们正在进行系统升级维护。请稍后再试。</p>
      
      <div class="maintenance-image">
        <img src="/images/maintenance.svg" alt="Maintenance" />
      </div>
      
      <div class="countdown-container">
        <p class="countdown-title">预计维护结束时间：</p>
        <div class="countdown-timer">
          <div class="countdown-item">
            <div class="countdown-value">{{ countdown.hours }}</div>
            <div class="countdown-label">时</div>
          </div>
          <div class="countdown-item">
            <div class="countdown-value">{{ countdown.minutes }}</div>
            <div class="countdown-label">分</div>
          </div>
          <div class="countdown-item">
            <div class="countdown-value">{{ countdown.seconds }}</div>
            <div class="countdown-label">秒</div>
          </div>
        </div>
      </div>
      
      <div class="notification-form">
        <p class="form-title">留下您的邮箱，系统恢复后我们会通知您</p>
        <div class="form-input-group">
          <q-input
            v-model="email"
            type="email"
            placeholder="请输入您的邮箱"
            outlined
            class="notification-input"
            :rules="[val => validateEmail(val) || '请输入有效的邮箱地址']"
          />
          <q-btn
            color="primary"
            label="提交"
            class="notification-button"
            :disable="!validateEmail(email)"
            @click="submitEmail"
          />
        </div>
      </div>
      
      <div class="contact-info">
        <p class="contact-title">如有紧急事项，请联系我们：</p>
        <div class="contact-methods">
          <div class="contact-item">
            <q-icon name="phone" color="primary" size="sm" class="q-mr-xs" />
            <span>************</span>
          </div>
          <div class="contact-item">
            <q-icon name="mail" color="primary" size="sm" class="q-mr-xs" />
            <span><EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();
const email = ref('');
const countdown = ref({
  hours: 2,
  minutes: 0,
  seconds: 0
});

// 设置维护结束时间（这里设置为当前时间后2小时）
const endTime = new Date();
endTime.setHours(endTime.getHours() + 2);

// 更新倒计时
const updateCountdown = () => {
  const now = new Date();
  const diff = endTime - now;
  
  if (diff <= 0) {
    // 如果已经到期，所有值设为0
    countdown.value = {
      hours: 0,
      minutes: 0,
      seconds: 0
    };
    clearInterval(timer);
    
    // 可以在这里添加自动刷新页面的逻辑
    // window.location.reload();
    
    return;
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  countdown.value = {
    hours,
    minutes,
    seconds
  };
};

// 验证邮箱格式
const validateEmail = (email) => {
  const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
};

// 提交邮箱
const submitEmail = () => {
  if (!validateEmail(email.value)) return;
  
  // 这里应该是实际的API调用
  // await fetch('/api/maintenance-notification', {
  //   method: 'POST',
  //   body: JSON.stringify({ email: email.value }),
  //   headers: { 'Content-Type': 'application/json' }
  // });
  
  // 显示成功提示
  $q.notify({
    color: 'positive',
    message: '提交成功！系统恢复后我们会通知您。',
    icon: 'check_circle',
    position: 'top',
    timeout: 3000
  });
  
  // 清空输入框
  email.value = '';
};

let timer;

onMounted(() => {
  updateCountdown();
  timer = setInterval(updateCountdown, 1000);
});

onBeforeUnmount(() => {
  clearInterval(timer);
});
</script>

<style lang="scss" scoped>
.maintenance-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.maintenance-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 40px 20px;
}

.logo-container {
  margin-bottom: 30px;
  
  .logo {
    max-width: 150px;
    height: auto;
  }
}

.title {
  font-size: 36px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 16px;
}

.description {
  font-size: 18px;
  color: #555;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.maintenance-image {
  max-width: 400px;
  margin: 0 auto 30px;
  
  img {
    width: 100%;
    height: auto;
  }
}

.countdown-container {
  margin-bottom: 40px;
}

.countdown-title {
  font-size: 18px;
  color: #333;
  margin-bottom: 16px;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.countdown-item {
  width: 70px;
  height: 80px;
  background-color: #1976d2;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 10px rgba(25, 118, 210, 0.2);
}

.countdown-value {
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
}

.countdown-label {
  font-size: 14px;
  margin-top: 8px;
}

.notification-form {
  max-width: 500px;
  margin: 0 auto 40px;
}

.form-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
}

.form-input-group {
  display: flex;
  gap: 10px;
}

.notification-input {
  flex-grow: 1;
}

.contact-info {
  max-width: 500px;
  margin: 0 auto;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.contact-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #1976d2;
}

@media (max-width: 767px) {
  .title {
    font-size: 28px;
  }
  
  .description {
    font-size: 16px;
  }
  
  .maintenance-image {
    max-width: 280px;
  }
  
  .countdown-title {
    font-size: 16px;
  }
  
  .countdown-timer {
    gap: 10px;
  }
  
  .countdown-item {
    width: 60px;
    height: 70px;
  }
  
  .countdown-value {
    font-size: 24px;
  }
  
  .countdown-label {
    font-size: 12px;
  }
  
  .form-input-group {
    flex-direction: column;
  }
  
  .notification-button {
    width: 100%;
  }
  
  .contact-title {
    font-size: 14px;
  }
  
  .contact-item {
    font-size: 14px;
  }
}
</style>
