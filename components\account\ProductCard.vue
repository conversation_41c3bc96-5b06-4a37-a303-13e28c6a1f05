<template>
  <q-card class="product-card">
    <!-- 商品图片 -->
    <q-img :src="product.image" :ratio="1" class="product-image" fit="cover">
      <template v-slot:loading>
        <q-spinner-dots color="white" />
      </template>
      <div class="absolute-top-right">
        <q-btn round flat color="white" text-color="negative" icon="delete" size="sm" class="delete-btn" @click.stop="$emit('remove', product.id)">
          <q-tooltip>{{ $t('wishlist.confirmRemove.title') }}</q-tooltip>
        </q-btn>
      </div>
      <div class="absolute-bottom product-hover-actions">
        <q-btn color="primary" :label="$t('wishlist.viewDetails')" size="sm" :to="`/products/${product.id}`" class="full-width" @click.stop />
      </div>
    </q-img>

    <!-- 商品信息 -->
    <q-card-section class="product-info q-pa-sm">
      <div class="product-name text-body2 ellipsis-2-lines">{{ product.name }}</div>
      <div class="row justify-between items-center q-mt-xs">
        <div class="product-price text-subtitle1 text-weight-bold text-negative"><span class="text-caption">￥</span>{{ product.price.toLocaleString() }}</div>
        <q-badge color="orange" outline class="q-px-sm">{{ $t('wishlist.hot') }}</q-badge>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { useI18n } from 'vue-i18n';

// 初始化国际化
useI18n();

defineProps({
  product: {
    type: Object,
    required: true,
  },
});

defineEmits(['remove']);
</script>

<style lang="scss" scoped>
.product-card {
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);

    .product-hover-actions {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.product-image {
  height: 180px;
  overflow: hidden;
  position: relative;

  :deep(.q-img__image) {
    transition: transform 0.5s ease;
    transform-origin: center center;
  }

  /* 覆盖删除按钮区域的背景色 */
  :deep(.q-img__content > div) {
    background: transparent !important;
    padding: 8px !important;
  }

  &:hover {
    :deep(.q-img__image) {
      transform: scale(1.05);
    }
  }
}

.product-hover-actions {
  background: rgba(0, 0, 0, 0.6);
  padding: 8px;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.product-info {
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fafafa;
}

.product-name {
  font-weight: 500;
  font-size: 13px;
  word-break: break-word;
  white-space: normal;
  color: #333;
}

.product-price {
  color: #ff4500;
  font-size: 16px;
}

.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 36px;
  min-height: 36px;
  line-height: 1.2;
}

.delete-btn {
  background-color: rgba(255, 255, 255, 0.7);
  opacity: 0.8;
  transition: all 0.3s ease;

  &:hover {
    opacity: 1;
    background-color: white;
    transform: scale(1.1);
  }
}

// 响应式调整
@media (max-width: 599px) {
  .product-image {
    height: 150px;
  }

  .product-info {
    height: 70px;
  }

  .product-name {
    font-size: 12px;
  }

  .ellipsis-2-lines {
    height: 32px;
    min-height: 32px;
  }

  .product-price {
    font-size: 14px;
  }
}
</style>
