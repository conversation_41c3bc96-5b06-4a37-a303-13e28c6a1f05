{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build:dev": "nuxt build --dotenv .env.dev", "build:pro": "nuxt build --dotenv .env.pro", "dev": "nuxt dev --dotenv .env.dev", "devdebug": "cross-env DEBUG=nuxt:* nuxt dev --dotenv .env.dev", "pro": "nuxt dev --dotenv .env.pro", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "^0.7.2", "@nuxtjs/i18n": "^9.1.0", "@nuxtjs/turnstile": "^0.9.11", "@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@pinia/nuxt": "^0.5.0", "@quasar/extras": "^1.16.15", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "dotenv": "^16.4.7", "eslint": "^9.16.0", "install": "^0.13.0", "npm": "^10.9.2", "nuxt": "^3.14.1592", "nuxt-quasar-ui": "^2.1.7", "pinia": "^2.3.0", "quasar": "^2.17.4", "sass": "^1.82.0", "sass-loader": "^16.0.4", "vue": "3.5.13", "vue-i18n": "^10.0.5", "vue-router": "^4.4.5", "vue3-country-intl": "^2.0.9"}, "devDependencies": {"vite-plugin-svg-icons": "^2.0.1"}}