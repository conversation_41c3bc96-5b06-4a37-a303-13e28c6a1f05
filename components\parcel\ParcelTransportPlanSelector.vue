<template>
  <div class="shipping-selector-section">
    <div class="row items-center q-mb-sm">
      <q-icon name="local_shipping" color="primary" size="sm" class="q-mr-sm" />
      <div class="text-h6">物流方案</div>
    </div>
    <div class="text-caption text-grey-8 q-mb-md">请选择适合您的物流方案</div>

    <q-card flat bordered class="q-pa-md">
      <div class="row items-center">
        <div v-if="modelValue" class="col-grow">
          <div class="row items-center justify-between">
            <div>
              <div class="text-body1 text-weight-medium">
                {{ modelValue.name }}
                <q-badge color="green" class="q-ml-sm">推荐</q-badge>
              </div>
              <div class="text-body2">单价: ¥{{ fen2yuan(modelValue.basePrice) }}/500g | 预计送达: {{ modelValue.estimatedDelivery }}</div>
              <div class="text-caption text-grey-8">{{ modelValue.description }}</div>
            </div>
            <div class="text-right">
              <div class="text-body1 text-primary text-weight-medium">¥{{ calculateShippingFee(modelValue) }}</div>
              <q-btn flat round color="primary" icon="edit" @click="openDialog" />
            </div>
          </div>
        </div>
        <div v-else class="col-grow text-center">
          <q-btn color="primary" label="选择物流方案" @click="openDialog" />
        </div>
      </div>
    </q-card>

    <!-- 物流方案选择弹窗 -->
    <q-dialog v-model="shippingDialog" persistent>
      <q-card style="width: 700px; max-width: 90vw">
        <q-card-section class="row items-center">
          <div class="text-h6">选择物流方案</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="text-body1 q-mb-md">
            包裹重量: <span class="text-weight-medium">{{ parcelWeight }} kg</span>
          </div>

          <q-tabs v-model="activeTab" dense class="text-grey" active-color="primary" indicator-color="primary" align="justify" narrow-indicator>
            <q-tab name="economy" label="经济" icon="savings" />
            <q-tab name="standard" label="标准" icon="local_shipping" />
            <q-tab name="express" label="快速" icon="rocket_launch" />
          </q-tabs>

          <q-separator />

          <q-tab-panels v-model="activeTab" animated>
            <q-tab-panel v-for="category in ['economy', 'standard', 'express']" :key="category" :name="category">
              <q-list separator>
                <q-item
                  v-for="shipping in filteredShippingMethods(category)"
                  :key="shipping.id"
                  clickable
                  :active="tempSelectedShipping && tempSelectedShipping.id === shipping.id"
                  active-class="bg-blue-1"
                  @click="selectShipping(shipping)">
                  <q-item-section>
                    <q-item-label class="text-weight-medium">
                      {{ shipping.name }}
                      <q-badge v-if="shipping.recommended" color="green" class="q-ml-sm">推荐</q-badge>
                    </q-item-label>
                    <q-item-label caption>单价: ¥{{ shipping.pricePerKg }}/kg | 预计送达: {{ shipping.estimatedDelivery }}</q-item-label>
                    <q-item-label caption>{{ shipping.description }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <div class="text-primary text-weight-medium">¥{{ calculateShippingFee(shipping) }}</div>
                  </q-item-section>
                </q-item>

                <div v-if="filteredShippingMethods(category).length === 0" class="text-center q-py-lg">
                  <q-icon name="info" size="2rem" color="grey-7" />
                  <div class="text-grey-7 q-mt-sm">暂无{{ getCategoryName(category) }}物流方案</div>
                </div>
              </q-list>
            </q-tab-panel>
          </q-tab-panels>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="grey" v-close-popup />
          <q-btn flat label="确认" color="primary" :disable="!tempSelectedShipping" @click="confirmShipping" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { fen2yuan } from '../../utils/utils';

// 定义组件接收的属性
const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  shippingMethods: {
    type: Array,
    required: true,
    default: () => [],
  },
  parcelWeight: {
    type: [Number, String],
    required: true,
    default: '0.00',
  },
});

// 定义组件向外发出的事件
const emit = defineEmits(['update:modelValue', 'calculate-fee']);

// 弹窗状态
const shippingDialog = ref(false);
const tempSelectedShipping = ref(props.modelValue);
const activeTab = ref('standard'); // 默认选中标准选项卡

// 过滤指定类别的物流方案
function filteredShippingMethods(category) {
  return props.shippingMethods.filter((method) => method.planType === category);
}

// 获取类别名称
function getCategoryName(category) {
  const categoryMap = {
    economy: '经济',
    standard: '标准',
    express: '快速',
  };
  return categoryMap[category] || category;
}

// 计算物流费用
function calculateShippingFee(shipping) {
  if (!shipping) return '0.00';
  const weight = parseFloat(props.parcelWeight);
  const fee = weight * shipping.basePrice;
  return fee.toFixed(2);
}

// 打开物流方案选择弹窗
function openDialog() {
  tempSelectedShipping.value = props.modelValue;

  // 如果有选中的物流方案，自动切换到对应的选项卡
  if (props.modelValue && props.modelValue.category) {
    activeTab.value = props.modelValue.category;
  }

  shippingDialog.value = true;
}

// 选择物流方案
function selectShipping(shipping) {
  tempSelectedShipping.value = shipping;
}

// 确认物流方案选择
function confirmShipping() {
  emit('update:modelValue', tempSelectedShipping.value);
  emit('calculate-fee');
  shippingDialog.value = false;
}

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    tempSelectedShipping.value = newValue;
  }
);
</script>

<style lang="scss" scoped>
.shipping-selector-section {
  .q-card {
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }

  .q-tab {
    min-height: 40px;
  }
}
</style>
