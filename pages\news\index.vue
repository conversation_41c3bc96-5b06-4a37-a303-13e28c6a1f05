<template>
  <Header />
  <div class="news-page">
    <div class="news-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">新闻公告</h1>
        <div class="page-subtitle">了解平台最新动态和重要通知</div>
      </div>

      <!-- 新闻分类 -->
      <div class="filter-section q-mb-lg">
        <div class="row items-center justify-between">
          <div class="filter-tabs">
            <q-tabs v-model="filter.category" class="text-primary" active-color="primary" indicator-color="primary" narrow-indicator dense>
              <q-tab name="all" label="全部" />
              <q-tab name="notice" label="系统公告" />
              <q-tab name="update" label="平台更新" />
              <q-tab name="policy" label="政策变动" />
              <q-tab name="promotion" label="活动预告" />
            </q-tabs>
          </div>
          <div class="filter-sort">
            <q-select v-model="filter.sort" :options="sortOptions" outlined dense options-dense emit-value map-options style="width: 150px" />
          </div>
        </div>
      </div>

      <!-- 置顶公告 -->
      <div v-if="pinnedNews.length > 0" class="pinned-news q-mb-xl">
        <h2 class="section-title">重要公告</h2>
        <q-list bordered separator>
          <q-item v-for="news in pinnedNews" :key="news.id" :to="`/news/${news.id}`" clickable v-ripple class="pinned-news-item">
            <q-item-section avatar>
              <q-icon :name="getCategoryIcon(news.category)" color="primary" size="md" />
            </q-item-section>

            <q-item-section>
              <q-item-label class="news-title">
                <q-badge color="red" class="q-mr-sm">置顶</q-badge>
                {{ news.title }}
              </q-item-label>
              <q-item-label caption lines="2" class="news-summary">
                {{ news.summary }}
              </q-item-label>
            </q-item-section>

            <q-item-section side>
              <div class="column items-end">
                <q-badge :color="getCategoryColor(news.category)" class="q-mb-xs">
                  {{ getCategoryText(news.category) }}
                </q-badge>
                <q-item-label caption>{{ formatDate(news.publishDate) }}</q-item-label>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- 新闻列表 -->
      <div class="news-list">
        <h2 class="section-title">{{ getFilterTitle() }}</h2>

        <div v-if="displayedNews.length > 0">
          <q-list bordered separator>
            <q-item v-for="news in displayedNews" :key="news.id" :to="`/news/${news.id}`" clickable v-ripple class="news-item">
              <q-item-section avatar>
                <q-icon :name="getCategoryIcon(news.category)" :color="getCategoryColor(news.category)" size="md" />
              </q-item-section>

              <q-item-section>
                <q-item-label class="news-title">
                  {{ news.title }}
                </q-item-label>
                <q-item-label caption lines="2" class="news-summary">
                  {{ news.summary }}
                </q-item-label>
                <div class="row items-center q-mt-xs">
                  <q-badge :color="getCategoryColor(news.category)" class="q-mr-sm">
                    {{ getCategoryText(news.category) }}
                  </q-badge>
                  <q-icon name="visibility" size="xs" class="q-mr-xs" />
                  <span class="text-grey-7 text-caption">{{ news.views }}</span>
                </div>
              </q-item-section>

              <q-item-section side>
                <div class="column items-end">
                  <q-item-label caption>{{ formatDate(news.publishDate) }}</q-item-label>
                  <q-item-label caption v-if="isNew(news.publishDate)" class="text-primary q-mt-xs">
                    <q-badge color="red">NEW</q-badge>
                  </q-item-label>
                </div>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <div v-else class="no-news">
          <q-icon name="article" size="4rem" color="grey-5" />
          <p class="text-grey-7 q-mt-md">暂无符合条件的新闻公告</p>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination-container q-mt-lg">
          <q-pagination v-model="currentPage" :max="totalPages" :max-pages="5" boundary-numbers direction-links @update:model-value="handlePageChange" class="justify-center" />
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';

const router = useRouter();
const route = useRoute();

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalNews = ref(0);

// 筛选和排序
const filter = ref({
  category: 'all',
  sort: 'latest',
});

const sortOptions = [
  { label: '最新发布', value: 'latest' },
  { label: '最多浏览', value: 'popular' },
];

// 从URL获取页码和筛选条件
onMounted(() => {
  if (route.query.page) {
    currentPage.value = parseInt(route.query.page) || 1;
  }
  if (route.query.category) {
    filter.value.category = route.query.category;
  }
  if (route.query.sort) {
    filter.value.sort = route.query.sort;
  }
  fetchNews();
});

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalNews.value / pageSize.value);
});

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
  updateRouteQuery();
  fetchNews();
};

// 更新路由查询参数
const updateRouteQuery = () => {
  router.push({
    path: '/news',
    query: {
      page: currentPage.value,
      category: filter.value.category,
      sort: filter.value.sort,
    },
  });
};

// 监听筛选条件变化
watch(
  () => [filter.value.category, filter.value.sort],
  () => {
    currentPage.value = 1; // 重置页码
    updateRouteQuery();
    fetchNews();
  }
);

// 模拟新闻数据
const newsData = ref([
  {
    id: 1,
    title: '系统维护通知：平台将于6月15日凌晨2点至6点进行系统升级',
    summary: '为提升用户体验，我们将于6月15日凌晨2点至6点进行系统升级维护，期间平台将暂停服务，给您带来的不便敬请谅解。',
    publishDate: '2023-06-10',
    category: 'notice',
    isPinned: true,
    views: 5678,
  },
  {
    id: 2,
    title: '平台新功能上线：全新个人中心和订单管理系统',
    summary: '我们很高兴地宣布，全新的个人中心和订单管理系统已正式上线，为您提供更便捷、更直观的用户体验。',
    publishDate: '2023-06-20',
    category: 'update',
    isPinned: false,
    views: 3456,
  },
  {
    id: 3,
    title: '关于调整国际物流费用的通知',
    summary: '受国际油价上涨和运力紧张影响，我们将从7月1日起调整部分国家和地区的国际物流费用，详情请查看公告。',
    publishDate: '2023-06-25',
    category: 'policy',
    isPinned: true,
    views: 4567,
  },
  {
    id: 4,
    title: '618年中大促即将开始，多重好礼等你来拿',
    summary: '618年中大促即将开始，全场商品低至5折，每满300减50，抢先预售享额外9折，618购物狂欢等你来！',
    publishDate: '2023-05-30',
    category: 'promotion',
    isPinned: false,
    views: 6789,
  },
  {
    id: 5,
    title: '关于防范网络诈骗的重要提醒',
    summary: '近期有不法分子冒充平台客服实施诈骗，请广大用户提高警惕，保护个人信息和财产安全。',
    publishDate: '2023-07-05',
    category: 'notice',
    isPinned: true,
    views: 7890,
  },
  {
    id: 6,
    title: '平台App 3.0版本更新说明',
    summary: '平台App 3.0版本已正式发布，带来全新界面设计和多项功能优化，提供更流畅、更智能的购物体验。',
    publishDate: '2023-07-10',
    category: 'update',
    isPinned: false,
    views: 2345,
  },
  {
    id: 7,
    title: '关于优化退货退款流程的公告',
    summary: '为提升用户体验，我们对退货退款流程进行了优化，简化了操作步骤，缩短了处理时间，详情请查看公告。',
    publishDate: '2023-07-15',
    category: 'policy',
    isPinned: false,
    views: 3456,
  },
  {
    id: 8,
    title: '夏日特惠活动预告：全场8折起',
    summary: '炎炎夏日，清凉特惠！夏日特惠活动即将开始，全场商品8折起，部分热门商品低至5折，还有限量夏日礼包等你来拿！',
    publishDate: '2023-07-20',
    category: 'promotion',
    isPinned: false,
    views: 5678,
  },
  {
    id: 9,
    title: '关于调整用户等级体系的通知',
    summary: '为回馈广大用户的支持，我们将于8月1日起调整用户等级体系，提供更多特权和福利，详情请查看公告。',
    publishDate: '2023-07-25',
    category: 'policy',
    isPinned: false,
    views: 4567,
  },
  {
    id: 10,
    title: '平台隐私政策更新通知',
    summary: '我们对平台隐私政策进行了更新，更新内容主要涉及个人信息收集和使用规则，请您仔细阅读并知悉相关内容。',
    publishDate: '2023-08-01',
    category: 'notice',
    isPinned: false,
    views: 3456,
  },
  {
    id: 11,
    title: '新增支付方式：支持Apple Pay和Google Pay',
    summary: '为提供更便捷的支付体验，我们新增了Apple Pay和Google Pay支付方式，支持快速安全的移动支付。',
    publishDate: '2023-08-05',
    category: 'update',
    isPinned: false,
    views: 2345,
  },
  {
    id: 12,
    title: '开学季活动预告：学生专享优惠',
    summary: '开学季来临，学生专享优惠！凭学生证购买指定商品享受额外9折优惠，还有机会赢取iPad等学习用品！',
    publishDate: '2023-08-10',
    category: 'promotion',
    isPinned: false,
    views: 4567,
  },
  {
    id: 13,
    title: '关于提高账户安全的重要通知',
    summary: '为保障用户账户安全，我们将于近期上线双因素认证功能，建议所有用户开启此功能，提高账户安全性。',
    publishDate: '2023-08-15',
    category: 'notice',
    isPinned: false,
    views: 5678,
  },
  {
    id: 14,
    title: '全新物流追踪系统上线公告',
    summary: '我们很高兴地宣布，全新的物流追踪系统已正式上线，支持实时追踪包裹位置，提供更精准的配送时间预估。',
    publishDate: '2023-08-20',
    category: 'update',
    isPinned: false,
    views: 3456,
  },
  {
    id: 15,
    title: '关于调整部分商品价格的通知',
    summary: '受原材料价格上涨和供应链成本增加影响，我们将从9月1日起调整部分商品的价格，详情请查看公告。',
    publishDate: '2023-08-25',
    category: 'policy',
    isPinned: false,
    views: 4567,
  },
]);

// 模拟API请求获取新闻列表
const fetchNews = () => {
  // 根据筛选条件过滤新闻
  let filteredNews = [...newsData.value];

  if (filter.value.category !== 'all') {
    filteredNews = filteredNews.filter((news) => news.category === filter.value.category);
  }

  // 根据排序条件排序新闻
  switch (filter.value.sort) {
    case 'latest':
      filteredNews.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
      break;
    case 'popular':
      filteredNews.sort((a, b) => b.views - a.views);
      break;
  }

  // 计算总新闻数
  totalNews.value = filteredNews.length;

  // 在实际应用中，这里应该是API调用
  // const response = await fetch(`/api/news?page=${currentPage.value}&pageSize=${pageSize.value}&category=${filter.value.category}&sort=${filter.value.sort}`);
  // const data = await response.json();
  // newsData.value = data.items;
  // totalNews.value = data.total;
};

// 置顶公告
const pinnedNews = computed(() => {
  return newsData.value.filter((news) => news.isPinned);
});

// 根据当前页码和页面大小计算要显示的新闻
const displayedNews = computed(() => {
  // 根据筛选条件过滤新闻
  let filteredNews = [...newsData.value];

  if (filter.value.category !== 'all') {
    filteredNews = filteredNews.filter((news) => news.category === filter.value.category);
  }

  // 排除置顶新闻
  filteredNews = filteredNews.filter((news) => !news.isPinned);

  // 根据排序条件排序新闻
  switch (filter.value.sort) {
    case 'latest':
      filteredNews.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
      break;
    case 'popular':
      filteredNews.sort((a, b) => b.views - a.views);
      break;
  }

  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredNews.slice(start, end);
});

// 格式化日期
const formatDate = (dateString) => {
  return date.formatDate(new Date(dateString), 'YYYY-MM-DD');
};

// 判断是否为新发布的新闻（7天内）
const isNew = (dateString) => {
  const publishDate = new Date(dateString);
  const now = new Date();
  const diffDays = Math.floor((now - publishDate) / (1000 * 60 * 60 * 24));
  return diffDays <= 7;
};

// 获取分类图标
const getCategoryIcon = (category) => {
  switch (category) {
    case 'notice':
      return 'announcement';
    case 'update':
      return 'update';
    case 'policy':
      return 'policy';
    case 'promotion':
      return 'campaign';
    default:
      return 'article';
  }
};

// 获取分类颜色
const getCategoryColor = (category) => {
  switch (category) {
    case 'notice':
      return 'red';
    case 'update':
      return 'green';
    case 'policy':
      return 'blue';
    case 'promotion':
      return 'orange';
    default:
      return 'grey';
  }
};

// 获取分类文本
const getCategoryText = (category) => {
  switch (category) {
    case 'notice':
      return '系统公告';
    case 'update':
      return '平台更新';
    case 'policy':
      return '政策变动';
    case 'promotion':
      return '活动预告';
    default:
      return '其他';
  }
};

// 获取筛选标题
const getFilterTitle = () => {
  switch (filter.value.category) {
    case 'notice':
      return '系统公告';
    case 'update':
      return '平台更新';
    case 'policy':
      return '政策变动';
    case 'promotion':
      return '活动预告';
    default:
      return '全部公告';
  }
};
</script>

<style lang="scss" scoped>
.news-page {
  padding: 20px 0 40px;
  background-color: #f8f8f8;
}

.news-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 18px;
  color: #666;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;

  &:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: #1976d2;
    margin-top: 10px;
  }
}

.filter-section {
  margin-bottom: 30px;
}

.pinned-news {
  margin-bottom: 40px;
}

.pinned-news-item {
  background-color: #f0f7ff;

  &:hover {
    background-color: #e3f2fd;
  }
}

.news-item {
  &:hover {
    background-color: #f5f5f5;
  }
}

.news-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
}

.news-summary {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 4px;
}

.no-news {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px;
}

@media (max-width: 767px) {
  .page-header {
    margin-bottom: 30px;
    padding: 20px 0;
  }

  .page-title {
    font-size: 26px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .section-title {
    font-size: 22px;
    margin-bottom: 15px;
  }

  .news-title {
    font-size: 15px;
  }

  .news-summary {
    font-size: 13px;
  }

  .pagination-container {
    margin: 30px 0 15px;
  }
}
</style>
