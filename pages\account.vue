<template>
  <Header />
  <!-- 主内容区域 -->
  <!-- 面包屑 -->
  <Breadcrumbs :breadcrumbs="breadcrumbs" :show-menu-button="true" @toggle-menu="toggleSideMenu" />

  <div class="main-content">
    <!-- 桌面端左侧菜单 - 只在大屏幕上显示 -->
    <div class="left-menu gt-sm">
      <UserMenu :items="menuItems" @navigate="navigate" />
    </div>

    <!-- 右侧内容 -->
    <div class="right-content">
      <!-- 动态加载的内容 -->
      <NuxtPage />
    </div>

    <!-- 移动端侧边菜单 - 使用对话框从右侧滑出 -->
    <q-dialog v-model="sideMenuOpen" position="right" full-height :maximized="$q.screen.lt.sm" transition-show="slide-left" transition-hide="slide-right">
      <q-card class="side-menu-card">
        <q-card-section class="q-pa-md q-pb-none">
          <div class="row items-center justify-between q-mb-md">
            <div class="text-subtitle1 text-weight-medium">
              <q-icon name="person" color="primary" size="sm" class="q-mr-xs" />
              {{ $t('menu.account') }}
            </div>
            <q-btn icon="close" flat round dense v-close-popup color="grey-7" />
          </div>
        </q-card-section>
        <q-card-section class="q-pa-md q-pt-none">
          <UserMenu :items="menuItems" @navigate="navigateAndClose" />
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>

  <Footer />
</template>

<script setup>
import { ref } from 'vue';
import UserMenu from '~/components/UserMenu.vue';
import Breadcrumbs from '~/components/Breadcrumbs.vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const $q = useQuasar();

// 侧边菜单控制
const sideMenuOpen = ref(false);

// 切换侧边菜单显示状态
const toggleSideMenu = () => {
  sideMenuOpen.value = !sideMenuOpen.value;
};

// 导航并关闭菜单
const navigateAndClose = (path) => {
  router.push(path);
  sideMenuOpen.value = false;
};

// 菜单项配置
const menuItems = [
  { label: 'menu.dashboard', icon: 'home', iconColor: 'primary', path: '/account' },
  {
    label: 'menu.assets',
    icon: 'account_balance_wallet',
    iconColor: 'green',
    opened: true,
    children: [
      { label: 'menu.balance', path: '/account/balance', icon: 'payments', iconColor: 'green-6' },
      { label: 'menu.coupons', path: '/account/coupons', icon: 'local_offer', iconColor: 'deep-orange' },
      { label: 'menu.points', path: '/account/points', icon: 'stars', iconColor: 'amber' },
    ],
  },
  {
    label: 'menu.orders',
    icon: 'shopping_bag',
    iconColor: 'blue',
    children: [
      { label: 'menu.orderProduct', path: '/account/orders', icon: 'shopping_cart', iconColor: 'primary' },
      { label: 'menu.orderTransfer', path: '/account/transfer', icon: 'local_shipping', iconColor: 'purple' },
    ],
  },
  { label: 'menu.warehouse', icon: 'warehouse', iconColor: 'blue-grey', path: '/account/stock' },
  { label: 'menu.parcel', icon: 'inventory_2', iconColor: 'teal', path: '/account/parcel' },
  { label: '售后服务', icon: 'services', iconColor: 'purple', path: '/account/after-sale' },
  {
    label: 'menu.messages',
    icon: 'notifications',
    iconColor: 'orange',
    children: [
      { label: 'menu.msgInbox', path: '/account/msg-inbox', icon: 'mail', iconColor: 'orange-8' },
      { label: 'menu.msgConsult', path: '/account/msg-consult', icon: 'question_answer', iconColor: 'orange-6' },
    ],
  },
  { label: 'menu.wishlist', icon: 'favorite', iconColor: 'pink', path: '/account/wishlist' },
  {
    label: 'menu.settings',
    icon: 'settings',
    iconColor: 'grey-7',
    children: [
      { label: 'menu.profile', path: '/account/profile', icon: 'person', iconColor: 'grey-8' },
      { label: 'menu.addresses', path: '/account/addresses', icon: 'location_on', iconColor: 'grey-8' },
      { label: 'menu.security', path: '/account/security', icon: 'security', iconColor: 'grey-8' },
    ],
  },
];

// 面包屑动态生成
const breadcrumbs = computed(() => {
  const pathSegments = route.path.split('/').filter(Boolean);
  const crumbs = [];
  let currentPath = '';

  pathSegments.forEach((segment) => {
    currentPath += `/${segment}`;
    const matchedItem = findLabelForPath(currentPath);
    if (matchedItem) {
      crumbs.push({
        label: t(matchedItem), // 使用国际化翻译
        to: currentPath,
      });
    }
  });
  crumbs.unshift({ label: t('menu.account'), to: '/account' }); // Account页
  return crumbs;
});

// 查找路径对应的菜单名称
const findLabelForPath = (path) => {
  for (const item of menuItems) {
    if (item.path === path && path != '/account') return item.label; //排除个人中心页
    if (item.children) {
      const child = item.children.find((child) => child.path === path);
      if (child) return child.label;
    }
  }
  return null;
};

// 菜单导航
const navigate = (path) => {
  router.push(path);
};
</script>

<style lang="scss" scoped>
.main-content {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  min-height: 800px;
  padding: 0 15px;

  @media (max-width: 767px) {
    padding: 0 10px;
    min-height: 600px;
  }
}

.left-menu {
  width: 220px;
  min-width: 220px; /* 确保最小宽度，防止被挤压 */
  margin-right: 20px;
  flex-shrink: 0; /* 防止菜单被压缩 */

  @media (max-width: 1023px) and (min-width: 768px) {
    width: 200px;
    min-width: 200px; /* 确保最小宽度，防止被挤压 */
    margin-right: 15px;
  }
}

.right-content {
  flex: 1;
  width: 100%;

  @media (max-width: 767px) {
    padding: 0;
  }
}

.side-menu-card {
  width: 280px;
  max-width: 90vw;
  border-radius: 0;

  @media (max-width: 599px) {
    width: 100%;
  }

  .q-icon {
    font-size: 1.2rem;
  }
}
</style>
