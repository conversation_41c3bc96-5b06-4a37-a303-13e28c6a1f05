<template>
  <div class="message-inbox">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="mail" size="xs" color="orange-8" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('messageCenter.inbox.pageTitle') }}</span>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section q-pa-md">
      <div class="row q-col-gutter-md items-center">
        <div class="col-12 col-sm-auto">
          <q-select v-model="filter.type" :options="messageTypeOptions" :label="$t('messageCenter.inbox.messageType')" outlined dense emit-value map-options options-dense class="message-filter" />
        </div>

        <div class="col-12 col-sm-auto">
          <q-select v-model="filter.readStatus" :options="readStatusOptions" :label="$t('messageCenter.inbox.readStatus')" outlined dense emit-value map-options options-dense class="message-filter" />
        </div>
        <div class="col-12 col-sm-auto">
          <q-input v-model="filter.keyword" outlined dense :placeholder="$t('messageCenter.inbox.searchPlaceholder')" class="message-search">
            <template #append>
              <q-icon name="search" @click="loadMessages" class="cursor-pointer" />
            </template>
          </q-input>
        </div>
        <div class="col-12 col-sm-auto q-gutter-x-sm">
          <q-btn color="primary" outline :label="$t('messageCenter.inbox.markAllRead')" @click="markAllAsRead" :disable="!hasUnread" />
          <q-btn color="negative" outline :label="$t('messageCenter.inbox.batchDelete')" @click="confirmDelete" :disable="selected.length === 0" />
        </div>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list q-pa-md">
      <!-- PC端表格视图 -->
      <div v-if="!$q.screen.lt.sm" class="desktop-view">
        <q-table
          :rows="messages"
          :columns="columns"
          row-key="id"
          :pagination="pagination"
          :loading="loading"
          selection="multiple"
          v-model:selected="selected"
          :rows-per-page-options="[10, 20, 50]"
          @request="onRequest"
          binary-state-sort
          flat
          bordered
          table-class="message-table"
          dense
          wrap-cells>
          <template #body="props">
            <q-tr :props="props" :class="{ 'bg-blue-1': !props.row.read }" @click="openMessage(props.row)" class="cursor-pointer">
              <q-td auto-width>
                <q-checkbox v-model="props.selected" size="xs" />
              </q-td>
              <q-td key="type" :props="props">
                <div class="row no-wrap items-center">
                  <q-icon :name="getMessageIcon(props.row.type)" :color="getMessageColor(props.row.type)" size="sm" />
                  <span class="q-ml-sm">{{ getMessageTypeName(props.row.type) }}</span>
                </div>
              </q-td>
              <q-td key="title" :props="props">
                <div class="row items-center">
                  <div :class="{ 'text-weight-bold': !props.row.read }">
                    {{ props.row.title }}
                  </div>
                </div>
              </q-td>
              <q-td key="content" :props="props" class="content-cell">
                <div class="ellipsis">{{ truncateText(props.row.content, 40) }}</div>
              </q-td>
              <q-td key="createTime" :props="props">
                {{ formatDateTime(props.row.createTime) }}
              </q-td>
              <q-td key="actions" :props="props" auto-width>
                <q-btn flat round dense icon="delete" color="grey" @click.stop="confirmDeleteSingle(props.row)" />
              </q-td>
            </q-tr>
          </template>

          <template #no-data>
            <div class="full-width row flex-center q-py-lg">
              <q-icon name="mail" size="2em" color="grey-5" />
              <div class="q-ml-sm text-grey-7">{{ $t('messageCenter.inbox.noMessages') }}</div>
            </div>
          </template>

          <template #pagination="scope">
            <div class="row items-center justify-end q-py-sm">
              <div class="col-auto q-mr-md text-grey-7">
                {{ $t('messageCenter.inbox.totalRecords', { total: scope.pagination.rowsNumber, current: pagination.page, pages: Math.ceil(scope.pagesNumber) }) }}
              </div>
              <div class="col-auto">
                <q-pagination v-model="pagination.page" :max="Math.ceil(scope.pagesNumber)" :max-pages="6" :boundary-links="$q.screen.gt.xs" :direction-links="true" @update:model-value="onRequest" />
              </div>
            </div>
          </template>
        </q-table>
      </div>

      <!-- 移动端卡片视图 -->
      <div v-else class="mobile-view">
        <div v-if="messages.length === 0" class="text-center q-py-xl">
          <q-icon name="mail" size="3em" color="grey-5" />
          <div class="q-mt-sm text-grey-7">{{ $t('messageCenter.inbox.noMessages') }}</div>
        </div>

        <q-list separator>
          <q-item v-for="message in messages" :key="message.id" clickable v-ripple :class="{ 'bg-blue-1': !message.read }" @click="openMessage(message)">
            <q-item-section avatar>
              <q-avatar :color="getMessageColor(message.type)" text-color="white">
                <q-icon :name="getMessageIcon(message.type)" />
              </q-avatar>
            </q-item-section>

            <q-item-section>
              <div class="row no-wrap items-center justify-between q-mb-xs">
                <q-item-label :class="{ 'text-weight-bold': !message.read }" class="col">
                  {{ message.title }}
                </q-item-label>
              </div>
              <div class="row no-wrap items-center justify-between q-mb-xs type-status-row">
                <div class="text-caption text-grey-7 type-label">{{ getMessageTypeName(message.type) }}</div>
                <div class="text-caption text-grey-7 status-label">{{ message.read ? $t('messageCenter.inbox.read') : $t('messageCenter.inbox.unread') }}</div>
              </div>
              <q-item-label caption>
                {{ truncateText(message.content, 40) }}
              </q-item-label>
              <div class="row no-wrap items-center justify-between q-mt-xs">
                <div class="text-caption text-grey-7">{{ formatDate(message.createTime) }}</div>
                <q-btn flat round dense icon="delete" color="grey" size="xs" @click.stop="confirmDeleteSingle(message)" />
              </div>
            </q-item-section>
          </q-item>
        </q-list>

        <!-- 移动端分页 -->
        <div class="row justify-end q-py-md q-px-sm">
          <div class="col-auto q-mr-md text-caption text-grey-7">{{ $t('messageCenter.inbox.totalRecords', { total: messages.length, current: pagination.page, pages: Math.ceil(totalPages) }) }}</div>
          <div class="col-auto">
            <q-pagination v-model="pagination.page" :max="Math.ceil(totalPages)" :max-pages="5" :boundary-links="false" :direction-links="true" @update:model-value="onRequest" />
          </div>
        </div>
      </div>
    </div>

    <!-- 消息详情弹窗 -->
    <q-dialog v-model="messageDialog" persistent>
      <q-card style="min-width: 350px; max-width: 80vw">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ currentMessage.title }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <div class="row items-center q-mb-sm">
            <q-icon :name="getMessageIcon(currentMessage.type)" :color="getMessageColor(currentMessage.type)" size="sm" class="q-mr-xs" />
            <span class="text-caption text-grey-7">{{ getMessageTypeName(currentMessage.type) }}</span>
            <q-space />
            <span class="text-caption text-grey-7">{{ formatDateTime(currentMessage.createTime) }}</span>
          </div>
          <div class="message-content q-py-md">
            {{ currentMessage.content }}
          </div>
          <div v-if="currentMessage.link" class="q-mt-md">
            <q-btn color="primary" :label="currentMessage.linkText || $t('messageCenter.inbox.viewDetails')" :to="currentMessage.link" v-close-popup />
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 删除确认弹窗 -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">{{ $t('messageCenter.inbox.confirmDelete') }}</span>
        </q-card-section>

        <q-card-section>
          {{ deleteMultiple ? $t('messageCenter.inbox.confirmDeleteMultiple', { count: selected.length }) : $t('messageCenter.inbox.confirmDeleteSingle') }}
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('messageCenter.inbox.cancel')" color="primary" v-close-popup />
          <q-btn flat :label="$t('messageCenter.inbox.delete')" color="negative" @click="deleteMessages" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useQuasar, date } from 'quasar';
import { useI18n } from 'vue-i18n';
// import MessageApi from '~/composables/messageApi'; // 实际项目中取消注释

const $q = useQuasar();
const { t } = useI18n();

// 筛选条件
const filter = reactive({
  type: 'all',
  readStatus: 'all',
  keyword: '',
});

// 消息类型选项
const messageTypeOptions = [
  { label: t('messageCenter.inbox.allMessages'), value: 'all' },
  { label: t('messageCenter.inbox.systemNotice'), value: 'system' },
  { label: t('messageCenter.inbox.orderNotice'), value: 'order' },
  { label: t('messageCenter.inbox.transferNotice'), value: 'transfer' },
  { label: t('messageCenter.inbox.accountNotice'), value: 'account' },
  { label: t('messageCenter.inbox.activityNotice'), value: 'activity' },
];

// 阅读状态选项
const readStatusOptions = [
  { label: t('messageCenter.inbox.allMessages'), value: 'all' },
  { label: t('messageCenter.inbox.read'), value: 'read' },
  { label: t('messageCenter.inbox.unread'), value: 'unread' },
];

// 表格列定义
const columns = [
  { name: 'type', align: 'left', label: t('messageCenter.inbox.typeColumn'), field: 'type', sortable: true, style: 'width: 120px; white-space: nowrap' },
  { name: 'title', align: 'left', label: t('messageCenter.inbox.titleColumn'), field: 'title', sortable: true, style: 'width: 180px' },
  { name: 'content', align: 'left', label: t('messageCenter.inbox.contentColumn'), field: 'content', style: 'max-width: 300px' },
  { name: 'createTime', align: 'center', label: t('messageCenter.inbox.timeColumn'), field: 'createTime', sortable: true, format: (val) => formatDateTime(val), style: 'width: 150px' },
  { name: 'actions', align: 'center', label: t('messageCenter.inbox.actionsColumn'), style: 'width: 80px' },
];

// 分页设置
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  sortBy: 'createTime',
  descending: true,
});

// 消息列表
const messages = ref([]);
const totalPages = ref(1);
const loading = ref(false);
const selected = ref([]);

// 消息详情弹窗
const messageDialog = ref(false);
const currentMessage = ref({});

// 删除确认弹窗
const deleteDialog = ref(false);
const deleteMultiple = ref(false);
const messageToDelete = ref(null);

// 计算是否有未读消息
const hasUnread = computed(() => {
  return messages.value.some((msg) => !msg.read);
});

// 加载消息列表
const loadMessages = async () => {
  loading.value = true;
  try {
    // 实际项目中使用API调用
    // const response = await MessageApi.getMessages({
    //   page: pagination.value.page,
    //   pageSize: pagination.value.rowsPerPage,
    //   type: filter.type,

    //   readStatus: filter.readStatus,
    //   keyword: filter.keyword,
    //   sortBy: pagination.value.sortBy,
    //   descending: pagination.value.descending
    // });

    // 模拟数据
    await new Promise((resolve) => setTimeout(resolve, 500));

    const mockMessages = generateMockMessages();
    messages.value = mockMessages.data;
    totalPages.value = Math.ceil(mockMessages.total / pagination.value.rowsPerPage);
  } catch (error) {
    console.error('加载消息失败', error);
    $q.notify({
      color: 'negative',
      message: t('messageCenter.inbox.loadFailed'),
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 表格请求处理
const onRequest = (props) => {
  if (props.pagination) {
    pagination.value = props.pagination;
  }
  loadMessages();
};

// 打开消息详情
const openMessage = (message) => {
  currentMessage.value = { ...message };
  messageDialog.value = true;

  // 如果消息未读，标记为已读
  if (!message.read) {
    markAsRead(message.id);
  }
};

// 标记消息为已读
const markAsRead = async (messageId) => {
  try {
    // 实际项目中使用API调用
    // await MessageApi.markAsRead(messageId);

    // 模拟标记为已读
    const index = messages.value.findIndex((msg) => msg.id === messageId);
    if (index !== -1) {
      messages.value[index].read = true;
    }
  } catch (error) {
    console.error('标记消息已读失败', error);
  }
};

// 标记所有消息为已读
const markAllAsRead = async () => {
  try {
    // 实际项目中使用API调用
    // await MessageApi.markAllAsRead();

    // 模拟标记所有为已读
    messages.value.forEach((msg) => {
      msg.read = true;
    });

    $q.notify({
      color: 'positive',
      message: t('messageCenter.inbox.markAllReadSuccess'),
      icon: 'check',
    });
  } catch (error) {
    console.error('标记所有消息已读失败', error);
    $q.notify({
      color: 'negative',
      message: t('messageCenter.inbox.markAllReadFailed'),
      icon: 'error',
    });
  }
};

// 确认删除单条消息
const confirmDeleteSingle = (message) => {
  messageToDelete.value = message;
  deleteMultiple.value = false;
  deleteDialog.value = true;
};

// 确认批量删除
const confirmDelete = () => {
  deleteMultiple.value = true;
  deleteDialog.value = true;
};

// 删除消息
const deleteMessages = async () => {
  try {
    if (deleteMultiple.value) {
      // 批量删除
      // 实际项目中使用API调用
      // await MessageApi.deleteMessages(selected.value.map(msg => msg.id));

      // 模拟批量删除
      const selectedIds = selected.value.map((msg) => msg.id);
      messages.value = messages.value.filter((msg) => !selectedIds.includes(msg.id));
      selected.value = [];
    } else {
      // 单条删除
      // 实际项目中使用API调用
      // await MessageApi.deleteMessage(messageToDelete.value.id);

      // 模拟单条删除
      messages.value = messages.value.filter((msg) => msg.id !== messageToDelete.value.id);
    }

    $q.notify({
      color: 'positive',
      message: t('messageCenter.inbox.deleteSuccess'),
      icon: 'check',
    });
  } catch (error) {
    console.error('删除消息失败', error);
    $q.notify({
      color: 'negative',
      message: t('messageCenter.inbox.deleteFailed'),
      icon: 'error',
    });
  }
};

// 获取消息类型图标
const getMessageIcon = (type) => {
  const icons = {
    system: 'info',
    order: 'shopping_bag',
    transfer: 'local_shipping',
    account: 'account_circle',
    activity: 'campaign',
  };
  return icons[type] || 'mail';
};

// 获取消息类型颜色
const getMessageColor = (type) => {
  const colors = {
    system: 'blue',
    order: 'primary',
    transfer: 'purple',
    account: 'green',
    activity: 'orange',
  };
  return colors[type] || 'grey';
};

// 获取消息类型名称
const getMessageTypeName = (type) => {
  const names = {
    system: t('messageCenter.inbox.systemNotice'),
    order: t('messageCenter.inbox.orderNotice'),
    transfer: t('messageCenter.inbox.transferNotice'),
    account: t('messageCenter.inbox.accountNotice'),
    activity: t('messageCenter.inbox.activityNotice'),
  };
  return names[type] || type;
};

// 文本截断
const truncateText = (text, length) => {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  const dateObj = new Date(timestamp);
  return date.formatDate(dateObj, 'YYYY-MM-DD HH:mm');
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const dateObj = new Date(timestamp);
  return date.formatDate(dateObj, 'YYYY-MM-DD');
};

// 生成模拟消息数据
const generateMockMessages = () => {
  const total = 35;
  const pageSize = pagination.value.rowsPerPage;
  const currentPage = pagination.value.page;

  const allMessages = [];
  const types = ['system', 'order', 'transfer', 'account', 'activity'];
  const now = new Date();

  for (let i = 0; i < total; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    const createTime = new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);
    const read = Math.random() > 0.3;

    let title, content, link, linkText;

    switch (type) {
      case 'system':
        title = `系统通知 #${i + 1}`;
        content = '尊敬的用户，系统将于近期进行升级维护，届时部分功能可能暂时无法使用，请您谅解。';
        break;
      case 'order':
        title = `订单状态更新 #${i + 1}`;
        content = `您的订单 #ORD${100000 + i} 已发货，预计3-5天送达，请注意查收。`;
        link = `/account/order-detail?id=${100000 + i}`;
        linkText = t('messageCenter.inbox.viewDetails');
        break;
      case 'transfer':
        title = `转运单状态更新 #${i + 1}`;
        content = `您的包裹 #PKG${200000 + i} 已到达海外仓库，正在等待打包。`;
        link = `/account/parcel-detail?id=${200000 + i}`;
        linkText = t('messageCenter.inbox.viewDetails');
        break;
      case 'account':
        title = `账户通知 #${i + 1}`;
        content = '您的账户安全等级较低，建议设置更复杂的密码并开启两步验证。';
        link = '/account/security';
        linkText = t('messageCenter.inbox.viewDetails');
        break;
      case 'activity':
        title = `活动通知 #${i + 1}`;
        content = '618购物节即将开始，多重优惠等您来领取！限时折扣，先到先得！';
        link = '/promotions';
        linkText = t('messageCenter.inbox.viewDetails');
        break;
    }

    allMessages.push({
      id: i + 1,
      type,
      title,
      content,
      createTime: createTime.getTime(),
      read,
      link,
      linkText,
    });
  }

  // 排序
  if (pagination.value.sortBy) {
    allMessages.sort((a, b) => {
      const aValue = a[pagination.value.sortBy];
      const bValue = b[pagination.value.sortBy];

      if (pagination.value.descending) {
        return aValue > bValue ? -1 : 1;
      } else {
        return aValue < bValue ? -1 : 1;
      }
    });
  }

  // 筛选
  let filteredMessages = [...allMessages];

  if (filter.type !== 'all') {
    filteredMessages = filteredMessages.filter((msg) => msg.type === filter.type);
  }

  if (filter.readStatus !== 'all') {
    const isRead = filter.readStatus === 'read';
    filteredMessages = filteredMessages.filter((msg) => msg.read === isRead);
  }

  if (filter.keyword) {
    const keyword = filter.keyword.toLowerCase();
    filteredMessages = filteredMessages.filter((msg) => msg.title.toLowerCase().includes(keyword) || msg.content.toLowerCase().includes(keyword));
  }

  // 分页
  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  const paginatedMessages = filteredMessages.slice(start, end);

  return {
    data: paginatedMessages,
    total: filteredMessages.length,
  };
};

// 页面加载时获取消息列表
onMounted(() => {
  loadMessages();
});
</script>

<style lang="scss" scoped>
.message-inbox {
  .filter-section {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  }

  .message-filter {
    min-width: 150px;
  }

  .message-search {
    min-width: 200px;
  }

  .message-list {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

    // 表格样式优化
    .q-table {
      table {
        table-layout: fixed; // 固定表格布局，防止内容挤压列宽
      }

      th,
      td {
        padding: 8px 12px; // 减小单元格内边距
      }
    }

    .message-table {
      width: 100%;
    }

    // 确保类型和状态在移动端显示在同一行
    .type-status-row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;

      .type-label,
      .status-label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .type-label {
        margin-right: 8px;
      }
    }

    td {
      font-size: 13px;
    }

    .q-checkbox {
      font-size: 0.8em;
    }

    .content-cell {
      .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
      }
    }
  }

  .message-content {
    white-space: pre-line;
    line-height: 1.6;
  }

  // 移动端样式
  @media (max-width: 599px) {
    .filter-section {
      .q-select,
      .q-input {
        margin-bottom: 8px;
      }
    }

    .mobile-view {
      .q-item {
        padding: 12px;
      }

      .q-checkbox {
        font-size: 0.8em;
      }

      // 强化移动端类型和状态在同一行显示
      .type-status-row {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;

        .type-label,
        .status-label {
          display: inline-block;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
