<template>
  <Header />
  <div class="blog-detail-page">
    <div class="blog-container">
      <!-- 博客内容 -->
      <div v-if="blog" class="blog-content-wrapper">
        <!-- 博客标题和元信息 -->
        <div class="blog-header">
          <h1 class="blog-title">{{ blog.title }}</h1>
          <div class="blog-meta">
            <div class="meta-item">
              <q-icon name="event" size="sm" class="q-mr-xs" />
              <span>{{ formatDate(blog.publishDate) }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="person" size="sm" class="q-mr-xs" />
              <span>{{ blog.author }}</span>
            </div>
            <div class="meta-item">
              <q-icon name="visibility" size="sm" class="q-mr-xs" />
              <span>{{ blog.views }} 阅读</span>
            </div>
          </div>
          <div class="blog-tags q-mt-sm">
            <q-chip v-for="tag in blog.tags" :key="tag" outline color="primary" size="sm" class="q-mr-xs">
              {{ tag }}
            </q-chip>
          </div>
        </div>

        <!-- 博客封面图 -->
        <div class="blog-cover q-my-md">
          <q-img :src="blog.coverImage" :ratio="16 / 9" class="rounded-borders" />
        </div>

        <!-- 博客正文内容 -->
        <div class="blog-content">
          <div v-html="blog.content"></div>
        </div>

        <!-- 分享和点赞 -->
        <div class="blog-actions q-mt-lg">
          <div class="row justify-between items-center">
            <div class="social-share">
              <q-btn flat round color="primary" icon="share" class="q-mr-sm">
                <q-tooltip>分享</q-tooltip>
              </q-btn>
              <q-btn flat round :color="isLiked ? 'red' : 'grey'" icon="favorite" @click="toggleLike">
                <q-tooltip>{{ isLiked ? '取消点赞' : '点赞' }}</q-tooltip>
              </q-btn>
              <span class="q-ml-xs">{{ likeCount }}</span>
            </div>
            <q-btn flat color="primary" to="/blog/list" class="back-btn">
              <q-icon name="arrow_back" class="q-mr-xs" />
              返回列表
            </q-btn>
          </div>
        </div>

        <!-- 相关文章 -->
        <div class="related-articles q-mt-xl">
          <h2 class="section-title">相关文章</h2>
          <div class="row q-col-gutter-md">
            <div v-for="article in relatedArticles" :key="article.id" class="col-12 col-sm-6 col-md-4">
              <q-card class="related-article-card">
                <q-img :src="article.coverImage" :ratio="16 / 9" />
                <q-card-section>
                  <div class="text-h6 ellipsis-2-lines">{{ article.title }}</div>
                  <div class="text-grey-8 q-mt-sm text-caption">
                    <q-icon name="event" size="xs" class="q-mr-xs" />
                    {{ formatDate(article.publishDate) }}
                  </div>
                </q-card-section>
                <q-card-actions>
                  <q-btn flat color="primary" :to="`/blog/detail/${article.id}`"> 阅读全文 </q-btn>
                </q-card-actions>
              </q-card>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载中状态 -->
      <div v-else-if="loading" class="loading-state">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-state">
        <q-icon name="error_outline" color="negative" size="3em" />
        <div class="q-mt-md">文章不存在或已被删除</div>
        <q-btn color="primary" to="/blog/list" class="q-mt-md"> 返回博客列表 </q-btn>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const blogId = computed(() => parseInt(route.params.id));

// 状态
const loading = ref(true);
const blog = ref(null);
const isLiked = ref(false);
const likeCount = ref(0);

// 模拟博客数据
const blogData = [
  {
    id: 1,
    title: '如何选择适合自己的海外代购商品',
    author: '购物专家',
    publishDate: '2023-06-15',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    summary: '在全球化的今天，海外代购已经成为了一种流行的购物方式。本文将为您介绍如何选择适合自己的海外代购商品，避免踩坑，获得最佳购物体验。',
    content: `<p>在全球化的今天，海外代购已经成为了一种流行的购物方式。越来越多的消费者通过代购渠道购买国外的商品，无论是因为价格优势、品质保证，还是因为国内无法购买到的独特商品。但是，如何选择适合自己的海外代购商品，避免踩坑，获得最佳购物体验，这是每个消费者都需要考虑的问题。</p>

    <h2>了解自己的需求</h2>
    <p>在选择海外代购商品之前，首先要明确自己的需求。是追求价格优势，还是寻找国内没有的特色商品？是为了品质保证，还是为了追求时尚潮流？明确需求可以帮助你更有针对性地选择代购商品。</p>

    <h2>选择可靠的代购渠道</h2>
    <p>可靠的代购渠道是海外购物的基础保障。可以通过以下几点来判断代购渠道的可靠性：</p>
    <ul>
      <li>查看代购的资质和经验，有经验的代购通常会更加专业</li>
      <li>了解代购的购买渠道，正规的代购会从官方渠道或授权经销商处购买商品</li>
      <li>查看其他消费者的评价，特别是关于商品真伪和售后服务的评价</li>
      <li>了解代购的退换货政策，以防收到不满意的商品</li>
    </ul>

    <h2>了解商品的真伪鉴别方法</h2>
    <p>海外代购中，商品真伪是消费者最关心的问题之一。在购买前，可以：</p>
    <ul>
      <li>了解该品牌商品的基本特征和常见的仿冒特点</li>
      <li>要求代购提供购买凭证和商品的实拍图片</li>
      <li>对于高价值商品，可以要求提供防伪码或序列号</li>
      <li>收到商品后，及时验证商品的真伪</li>
    </ul>

    <h2>考虑物流和关税因素</h2>
    <p>海外代购涉及国际物流，需要考虑：</p>
    <ul>
      <li>物流时间：不同国家和不同物流方式的时间差异很大</li>
      <li>物流安全：选择有保障的物流方式，避免商品在运输过程中损坏</li>
      <li>关税问题：了解目标国家的关税政策，避免额外的关税支出</li>
      <li>物流跟踪：选择可以全程跟踪的物流方式，随时了解商品的位置</li>
    </ul>

    <h2>比较价格和服务</h2>
    <p>在选择海外代购商品时，不仅要看商品本身的价格，还要考虑：</p>
    <ul>
      <li>代购费用：不同代购的服务费可能差异很大</li>
      <li>物流费用：国际物流的费用通常不低</li>
      <li>可能的关税：某些商品可能需要缴纳高额关税</li>
      <li>售后服务：考虑代购提供的售后保障</li>
    </ul>

    <h2>总结</h2>
    <p>选择适合自己的海外代购商品需要综合考虑多种因素，包括自己的需求、代购渠道的可靠性、商品的真伪、物流和关税因素以及价格和服务的比较。通过仔细的研究和比较，可以找到最适合自己的海外代购商品，享受全球购物的乐趣。</p>`,
    tags: ['购物指南', '海外代购', '选品技巧'],
    views: 1256,
    likes: 89,
  },
  {
    id: 2,
    title: '2023年最受欢迎的10大日本美妆产品',
    author: '美妆达人',
    publishDate: '2023-07-22',
    coverImage: 'https://cdn.quasar.dev/img/parallax2.jpg',
    summary: '日本美妆产品以其高品质和创新性在全球享有盛誉。本文盘点了2023年最受欢迎的10大日本美妆产品，从面霜到防晒，一应俱全。',
    content: `<p>日本美妆产品以其高品质和创新性在全球享有盛誉。2023年，日本美妆市场继续推出了许多令人惊艳的产品，从面霜到防晒，一应俱全。本文将盘点2023年最受欢迎的10大日本美妆产品，帮助你在海外代购时做出明智的选择。</p>

    <h2>1. SK-II 护肤精华露</h2>
    <p>SK-II的护肤精华露（又称"神仙水"）一直是日本美妆的标志性产品。2023年的新配方添加了更多的抗氧化成分，提供更强的保湿和抗衰老效果。</p>

    <h2>2. 资生堂红腰子精华</h2>
    <p>资生堂的红腰子精华是抗衰老领域的佼佼者。2023年的版本增加了新的抗氧化成分，帮助减少细纹和皱纹，提升肌肤弹性。</p>

    <h2>3. 安耐晒金瓶防晒霜</h2>
    <p>安耐晒的金瓶防晒霜以其高效的防晒能力和轻薄的质地而闻名。2023年的配方更加防水和耐汗，同时不会给肌肤带来负担。</p>

    <h2>4. 花王Curel保湿面霜</h2>
    <p>专为敏感肌肤设计的Curel保湿面霜，2023年的配方增加了神经酰胺成分，提供更深层次的保湿效果，帮助修复受损的皮肤屏障。</p>

    <h2>5. DHC橄榄卸妆油</h2>
    <p>DHC的橄榄卸妆油是一款经典产品，能够轻松溶解彩妆和防晒霜。2023年的配方添加了更多的橄榄油和抗氧化成分，卸妆的同时滋养肌肤。</p>

    <h2>6. 肌美精3D面膜</h2>
    <p>肌美精的3D面膜以其独特的立体剪裁和丰富的精华液而受到欢迎。2023年推出了多种新的功效类型，包括美白、保湿和抗衰老等。</p>

    <h2>7. 城野医生毛孔收敛水</h2>
    <p>城野医生的毛孔收敛水是控油和收缩毛孔的利器。2023年的配方增加了水杨酸和甘草提取物，帮助控制油脂分泌，减少痘痘的产生。</p>

    <h2>8. SUQQU粉底液</h2>
    <p>SUQQU的粉底液以其自然的妆效和持久的遮瑕能力而闻名。2023年的配方添加了更多的保湿成分，适合各种肤质，特别是干燥和混合性肌肤。</p>

    <h2>9. 资生堂睫毛夹</h2>
    <p>资生堂的睫毛夹是美妆爱好者的必备工具。2023年的设计更加人体工学，适合各种眼型，能够轻松夹出自然卷翘的睫毛。</p>

    <h2>10. Canmake腮红</h2>
    <p>Canmake的腮红以其平价和高品质而受到欢迎。2023年推出了多种新色，从自然的裸粉到明亮的珊瑚色，适合各种肤色和场合。</p>

    <h2>总结</h2>
    <p>2023年的日本美妆市场继续展现其创新和高品质的特点。无论你是寻找基础护肤品、防晒产品还是彩妆，日本美妆都能提供多种优质选择。在海外代购时，这些产品绝对值得考虑。</p>`,
    tags: ['美妆', '日本产品', '热门榜单'],
    views: 3421,
    likes: 245,
  },
  {
    id: 3,
    title: '海外转运攻略：如何节省国际物流费用',
    author: '物流专家',
    publishDate: '2023-08-05',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    summary: '国际物流费用是海外购物的一大开支。本文分享了多种节省海外转运费用的方法和技巧，帮助您在享受全球购物的同时，降低物流成本。',
    content: `<p>国际物流费用是海外购物的一大开支，有时甚至可能超过商品本身的价格。本文将分享多种节省海外转运费用的方法和技巧，帮助您在享受全球购物的同时，降低物流成本。</p>

    <h2>了解不同转运方式的特点</h2>
    <p>国际物流主要有以下几种方式：</p>
    <ul>
      <li><strong>国际快递</strong>：如DHL、FedEx、UPS等，速度快但价格较高</li>
      <li><strong>邮政小包</strong>：价格相对便宜，但速度较慢，适合小件物品</li>
      <li><strong>专业转运公司</strong>：提供仓储、合箱等服务，可以根据需求选择不同的物流渠道</li>
      <li><strong>海运</strong>：价格最低，但时间最长，适合大件物品或不着急的商品</li>
    </ul>

    <h2>选择合适的转运公司</h2>
    <p>不同的转运公司有不同的优势和劣势，选择时可以考虑以下因素：</p>
    <ul>
      <li>转运费用：包括基础运费、首重费、续重费等</li>
      <li>增值服务：如合箱、拍照、验货、保险等</li>
      <li>目的地覆盖：确保转运公司能够送达你所在的地区</li>
      <li>客户评价：了解其他用户的使用体验</li>
      <li>客服质量：遇到问题时能否得到及时解决</li>
    </ul>

    <h2>利用合箱服务节省费用</h2>
    <p>合箱是节省转运费用的重要方法：</p>
    <ul>
      <li>多个包裹合并为一个包裹，可以节省首重费用</li>
      <li>减少包装材料，降低总重量</li>
      <li>一次性支付关税，可能比多次支付更划算</li>
    </ul>
    <p>使用合箱服务时，需要注意包裹的到达时间，确保所有包裹都到达转运仓库后再申请合箱。</p>

    <h2>选择经济的物流渠道</h2>
    <p>如果不着急收到商品，可以选择更经济的物流渠道：</p>
    <ul>
      <li>海运：虽然时间长（通常需要1-3个月），但价格最低，适合大件物品</li>
      <li>SAL（Surface Air Lifted）：介于航空和海运之间的方式，价格和时间都适中</li>
      <li>普通航空：比快递慢，但比海运快，价格适中</li>
    </ul>

    <h2>减轻包裹重量</h2>
    <p>国际物流费用通常按重量计算，减轻包裹重量可以直接节省费用：</p>
    <ul>
      <li>要求转运公司去除原始包装</li>
      <li>去除不必要的填充物</li>
      <li>选择轻量化的商品，例如选择塑料包装而非玻璃包装</li>
    </ul>

    <h2>利用优惠活动和会员折扣</h2>
    <p>许多转运公司会定期推出优惠活动：</p>
    <ul>
      <li>节日促销：如黑色星期五、圣诞节等</li>
      <li>新用户优惠：首次使用的折扣或优惠券</li>
      <li>会员等级折扣：长期使用同一转运公司可以获得会员折扣</li>
      <li>特定路线优惠：某些目的地可能有特别优惠</li>
    </ul>

    <h2>避开额外费用</h2>
    <p>国际物流中可能存在多种额外费用，提前了解并避开这些费用可以节省开支：</p>
    <ul>
      <li>超尺寸费用：大件物品可能需要支付额外费用</li>
      <li>仓储费：包裹在仓库停留时间过长可能产生仓储费</li>
      <li>保险费：根据物品价值决定是否需要购买保险</li>
      <li>关税和税费：了解目的地国家的关税政策，选择合适的申报方式</li>
    </ul>

    <h2>总结</h2>
    <p>节省海外转运费用需要综合考虑多种因素，包括转运方式、转运公司、合箱服务、物流渠道、包裹重量、优惠活动以及额外费用等。通过合理规划和选择，可以在不影响购物体验的情况下，显著降低国际物流成本。</p>`,
    tags: ['物流', '省钱技巧', '海外转运'],
    views: 2187,
    likes: 156,
  },
];

// 获取博客详情
onMounted(async () => {
  try {
    loading.value = true;

    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 在实际应用中，这里应该是API调用
    // const response = await fetch(`/api/blogs/${blogId.value}`);
    // const data = await response.json();
    // blog.value = data;

    // 使用模拟数据
    const foundBlog = blogData.find((item) => item.id === blogId.value);
    if (foundBlog) {
      blog.value = foundBlog;
      likeCount.value = foundBlog.likes || 0;
    }

    loading.value = false;
  } catch (error) {
    console.error('Failed to fetch blog details:', error);
    loading.value = false;
  }
});

// 相关文章（根据标签匹配）
const relatedArticles = computed(() => {
  if (!blog.value) return [];

  // 找出与当前博客标签相似的其他博客
  return blogData
    .filter((item) => item.id !== blogId.value) // 排除当前博客
    .filter((item) => {
      // 检查是否有共同的标签
      return item.tags.some((tag) => blog.value.tags.includes(tag));
    })
    .slice(0, 3); // 最多显示3篇相关文章
});

// 点赞功能
const toggleLike = () => {
  isLiked.value = !isLiked.value;
  likeCount.value += isLiked.value ? 1 : -1;

  // 在实际应用中，这里应该调用API更新点赞状态
  // await fetch(`/api/blogs/${blogId.value}/like`, {
  //   method: 'POST',
  //   body: JSON.stringify({ liked: isLiked.value }),
  //   headers: { 'Content-Type': 'application/json' }
  // });
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};
</script>

<style lang="scss" scoped>
.blog-detail-page {
  padding: 20px 0 40px;
}

.blog-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 16px;
}

.blog-content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  padding: 24px;
}

.blog-header {
  text-align: center;
  margin-bottom: 20px;
}

.blog-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  line-height: 1.3;
}

.blog-meta {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 16px;
  color: #666;
  font-size: 14px;

  .meta-item {
    display: flex;
    align-items: center;
  }
}

.blog-tags {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.blog-cover {
  margin: 24px 0;
  border-radius: 8px;
  overflow: hidden;
}

.blog-content {
  font-size: 16px;
  line-height: 1.7;
  color: #333;

  :deep(h2) {
    font-size: 22px;
    font-weight: bold;
    margin: 24px 0 16px;
    color: #1976d2;
  }

  :deep(p) {
    margin-bottom: 16px;
  }

  :deep(ul),
  :deep(ol) {
    margin-bottom: 16px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 16px 0;
  }

  :deep(a) {
    color: #1976d2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.blog-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.social-share {
  display: flex;
  align-items: center;
}

.back-btn {
  font-size: 14px;
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.related-article-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
}

.ellipsis-2-lines {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #666;
}

@media (max-width: 767px) {
  .blog-content-wrapper {
    padding: 16px;
  }

  .blog-title {
    font-size: 22px;
    margin-bottom: 12px;
  }

  .blog-meta {
    font-size: 12px;
    gap: 12px;
  }

  .blog-content {
    font-size: 15px;

    :deep(h2) {
      font-size: 18px;
      margin: 20px 0 12px;
    }
  }

  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
  }
}
</style>
