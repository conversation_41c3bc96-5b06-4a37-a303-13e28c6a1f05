<template>
  <div class="head">
    <div class="head-content">
      <NuxtLink to="/" class="logo-container">
        <img class="logo" src="/images/logo.png" alt="" />
      </NuxtLink>

      <span class="slogan gt-xs">全球领先的代购电商服务平台</span>
    </div>
  </div>
</template>

<script setup>
import { useResponsive } from '~/composables/useResponsive';
const { isMobile } = useResponsive();
</script>

<style lang="scss" scoped>
.head {
  width: 100%;
  background-color: #fff;
  position: relative;

  .head-content {
    max-width: 1200px;
    width: 100%;
    height: 60px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 0 15px;

    @media (max-width: 599px) {
      height: 50px;
      justify-content: center;
      padding: 0 10px;
    }
  }

  .logo-container {
    display: flex;
    align-items: center;
  }

  .logo {
    width: 260px;
    height: 60px;

    @media (max-width: 1023px) {
      width: 220px;
      height: 50px;
    }

    @media (max-width: 599px) {
      width: 180px;
      height: 42px;
    }
  }

  .slogan {
    font-size: 24px;
    margin-left: 50px;

    @media (max-width: 1023px) {
      font-size: 20px;
      margin-left: 30px;
    }
  }
}
</style>
