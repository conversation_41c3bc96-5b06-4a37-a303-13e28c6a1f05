const ProductApi = {
  // 查询商品
  getSpuDetail: (id) => {
    return useServerGet('/product/spu/get-detail', {
      params: { id },
    });
  },

  // 搜索商品（分页）
  searchProducts: (params) => {
    return useServerGet('/product/spu/page', {
      params,
      custom: {
        showLoading: true,
      },
    });
  },

  /**
   * 将后端返回的商品数据转换为前端模板数据格式
   *
   * @param backendData 后端返回的商品数据
   * @returns 转换后的前端商品数据
   */
  convertBackendProductToTemplate: (backendData) => {
    if (!backendData) return null;

    // 合并所有图片到一个数组中
    const images = [];
    const propertyList = ProductApi.convertProductPropertyList(backendData.skus);

    // 添加商品的主图 picUrl
    if (backendData.picUrl) {
      images.push({
        image_id: images.length,
        id: backendData.id,
        alt: '主图', // 主图的 alt 描述
        src: backendData.picUrl,
        variant_id: null, // 主图没有关联特定的 SKU
      });
    }

    // 添加轮播图 sliderPicUrls
    if (backendData.sliderPicUrls && Array.isArray(backendData.sliderPicUrls)) {
      backendData.sliderPicUrls.forEach((url, index) => {
        images.push({
          image_id: images.length,
          id: backendData.id,
          alt: `轮播图 ${index + 1}`, // 轮播图的 alt 描述
          src: url,
          variant_id: null, // 轮播图没有关联特定的 SKU
        });
      });
    }

    // 添加每个 SKU 的图片
    if (backendData.skus && Array.isArray(backendData.skus)) {
      backendData.skus.forEach((sku) => {
        if (sku.picUrl) {
          images.push({
            image_id: images.length,
            id: backendData.id,
            alt: `SKU 图片 (${sku.properties.map((prop) => prop.valueName).join(', ')})`, // 使用 SKU 属性生成 alt 描述
            src: sku.picUrl,
            variant_id: sku.id, // 关联到具体的 SKU
          });
        }
      });
    }

    // 返回转换后的模板商品数据
    return {
      id: backendData.id,
      title: backendData.name,
      introduction: backendData.introduction,
      description: backendData.description || '', // 简介
      type: backendData.type, // 类型字段后端没有，设置为空
      brand: '', // 品牌字段后端没有，设置为空
      collection: [], // 集合字段后端没有，设置为空数组
      category: backendData.categoryId, // 后端的 categoryId 可以映射为具体分类名称，这里需额外逻辑处理
      price: backendData.price, // 单价
      cartPrice: backendData.price, // 购物车价格和价格默认保持一致
      sale: false, // 是否促销
      discount: backendData.price < backendData.marketPrice ? (((backendData.marketPrice - backendData.price) / backendData.marketPrice) * 100).toFixed(0) : '', // 折扣百分比
      stock: backendData.stock, // 库存
      freight: backendData.freight, //运费
      shopName: backendData.shopName || ' ', //店铺名称
      source: backendData.source, //来源
      sourceLink: backendData.sourceLink, //来源链接
      new: false, // 是否新品，后端没有，默认为 false
      tags: [], // 标签后端没有，可以添加需要的标签逻辑
      variants: backendData.skus.map((sku) => ({
        variant_id: sku.id,
        id: backendData.id,
        sku: sku.id, // SKU 编号
        size: sku.properties.find((prop) => prop.propertyName === '尺寸')?.valueName || '', // 取尺寸属性值
        color: sku.properties.find((prop) => prop.propertyName === '颜色')?.valueName || '', // 取颜色属性值
        image_id: sku.id, // 假设 SKU 编号作为图片关联 ID
      })),
      images, // 所有图片按顺序组织
      skus: backendData.skus,
      propertyList: propertyList,
    };
  },

  /**
   * 从商品 SKU 数组中，转换出商品属性的数组
   *
   * 类似结构：[{
   *    id: // 属性的编号
   *    name: // 属性的名字
   *    values: [{
   *      id: // 属性值的编号
   *      name: // 属性值的名字
   *    }]
   * }]
   *
   * @param skus 商品 SKU 数组
   */
  convertProductPropertyList: (skus) => {
    let result = [];
    if (!skus) {
      return result;
    }
    for (const sku of skus) {
      if (!sku.properties) {
        continue;
      }
      for (const property of sku.properties) {
        // ① 先处理属性
        let resultProperty = result.find((item) => item.id === property.propertyId);
        if (!resultProperty) {
          resultProperty = {
            id: property.propertyId,
            name: property.propertyName,
            values: [],
          };
          result.push(resultProperty);
        }
        // ② 再处理属性值
        let resultValue = resultProperty.values.find((item) => item.id === property.valueId);
        if (!resultValue) {
          resultProperty.values.push({
            id: property.valueId,
            name: property.valueName,
          });
        }
      }
    }
    return result;
  },
};
export default ProductApi;
