/**
 * 自定义订单API接口
 * 用于处理用户自定义商品订单的创建、管理等功能
 */

const CustomOrderApi = {

  /**
   * 创建自定义订单
   * @param {Object} orderData - 订单数据
   * @param {string} orderData.productName - 商品名称
   * @param {string} orderData.productLink - 商品链接
   * @param {string} orderData.specifications - 规格说明
   * @param {string} orderData.productNotes - 商品备注
   * @param {Array} orderData.images - 商品图片数组
   * @param {number} orderData.unitPrice - 单价（分）
   * @param {number} orderData.quantity - 数量
   * @param {number} orderData.domesticShipping - 国内运费（分）
   * @param {string} orderData.currency - 货币类型，默认CNY
   * @returns {Promise} API响应
   */
  createCustomOrder: (orderData) => {
    return useClientPost('/order/custom/create', {
      params: orderData,
      custom: {
        showLoading: true,
      },
    });
  },

  /**
   * 上传商品图片
   * @param {File} file - 图片文件
   * @param {string} orderId - 订单ID（可选，用于关联已存在的订单）
   * @returns {Promise} 上传结果
   */
  uploadProductImage: (file, orderId = null) => {
    const formData = new FormData();
    formData.append('file', file);
    if (orderId) {
      formData.append('orderId', orderId);
    }

    return useClientPost('/order/custom/upload-image', {
      body: formData,
      custom: {
        showLoading: true,
      },
    });
  },

  /**
   * 批量上传商品图片
   * @param {Array<File>} files - 图片文件数组
   * @param {string} orderId - 订单ID（可选）
   * @returns {Promise} 上传结果
   */
  uploadProductImages: (files, orderId = null) => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files`, file);
    });
    if (orderId) {
      formData.append('orderId', orderId);
    }

    return useClientPost('/order/custom/upload-images', {
      body: formData,
      custom: {
        showLoading: true,
      },
    });
  },

  /**
   * 获取费用估算
   * @param {Object} params - 估算参数
   * @param {string} params.productLink - 商品链接
   * @param {number} params.quantity - 数量
   * @param {string} params.targetCountry - 目标国家代码
   * @returns {Promise} 费用估算结果
   */
  getShippingEstimate: (params) => {
    return useClientGet('/order/custom/shipping-estimate', {
      params,
    });
  },

  /**
   * 获取自定义订单列表
   * @param {Object} params - 查询参数
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页大小
   * @param {string} params.status - 订单状态（可选）
   * @returns {Promise} 订单列表
   */
  getCustomOrderList: (params) => {
    return useClientGet('/order/custom/list', {
      params,
    });
  },

  /**
   * 获取自定义订单详情
   * @param {string} orderId - 订单ID
   * @returns {Promise} 订单详情
   */
  getCustomOrderDetail: (orderId) => {
    return useClientGet('/order/custom/detail', {
      params: { orderId },
    });
  },

  /**
   * 更新自定义订单
   * @param {string} orderId - 订单ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise} 更新结果
   */
  updateCustomOrder: (orderId, updateData) => {
    return useClientPut('/order/custom/update', {
      params: {
        orderId,
        ...updateData,
      },
    });
  },

  /**
   * 取消自定义订单
   * @param {string} orderId - 订单ID
   * @param {string} reason - 取消原因
   * @returns {Promise} 取消结果
   */
  cancelCustomOrder: (orderId, reason) => {
    return useClientPost('/order/custom/cancel', {
      params: {
        orderId,
        reason,
      },
    });
  },

  /**
   * 确认自定义订单并进入支付流程
   * @param {string} orderId - 订单ID
   * @returns {Promise} 支付信息
   */
  confirmCustomOrder: (orderId) => {
    return useClientPost('/order/custom/confirm', {
      params: { orderId },
      custom: {
        showLoading: true,
      },
    });
  },

  /**
   * 获取支持的货币列表
   * @returns {Promise} 货币列表
   */
  getSupportedCurrencies: () => {
    return useClientGet('/order/custom/currencies');
  },

  /**
   * 获取运费计算规则
   * @param {string} targetCountry - 目标国家代码
   * @returns {Promise} 运费规则
   */
  getShippingRules: (targetCountry) => {
    return useClientGet('/order/custom/shipping-rules', {
      params: { targetCountry },
    });
  },

  /**
   * 验证商品链接
   * @param {string} productLink - 商品链接
   * @returns {Promise} 验证结果
   */
  validateProductLink: (productLink) => {
    return useClientPost('/order/custom/validate-link', {
      params: { productLink },
    });
  },

  /**
   * 获取商品信息建议（基于链接自动填充）
   * @param {string} productLink - 商品链接
   * @returns {Promise} 商品信息建议
   */
  getProductSuggestions: (productLink) => {
    return useClientPost('/order/custom/product-suggestions', {
      params: { productLink },
      custom: {
        showLoading: true,
      },
    });
  },

};

export default CustomOrderApi;
