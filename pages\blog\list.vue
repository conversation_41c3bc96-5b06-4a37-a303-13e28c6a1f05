<template>
  <Header />
  <div class="blog-list-page">
    <div class="blog-container">
      <h1 class="page-title">博客文章</h1>

      <!-- 博客列表 -->
      <div class="blog-list">
        <div v-for="blog in displayedBlogs" :key="blog.id" class="blog-item q-mb-md">
          <div class="row q-col-gutter-md">
            <!-- 博客图片 -->
            <div class="col-12 col-md-4">
              <q-img :src="blog.coverImage" :ratio="16 / 9" class="rounded-borders blog-image" fit="cover" />
            </div>

            <!-- 博客内容 -->
            <div class="col-12 col-md-8">
              <div class="blog-content">
                <h2 class="blog-title">
                  <router-link :to="`/blog/detail/${blog.id}`" class="blog-link">
                    {{ blog.title }}
                  </router-link>
                </h2>
                <div class="blog-meta q-mb-sm">
                  <q-icon name="event" size="xs" class="q-mr-xs" />
                  <span class="text-grey-7">{{ formatDate(blog.publishDate) }}</span>
                  <q-icon name="person" size="xs" class="q-ml-md q-mr-xs" />
                  <span class="text-grey-7">{{ blog.author }}</span>
                  <q-icon name="visibility" size="xs" class="q-ml-md q-mr-xs" />
                  <span class="text-grey-7">{{ blog.views }} 阅读</span>
                </div>
                <p class="blog-summary">{{ blog.summary }}</p>
                <div class="blog-tags">
                  <q-chip v-for="tag in blog.tags" :key="tag" size="sm" outline color="primary" class="q-mr-xs">
                    {{ tag }}
                  </q-chip>
                </div>
                <q-btn flat color="primary" :to="`/blog/detail/${blog.id}`" class="read-more-btn q-mt-sm">
                  阅读全文
                  <q-icon name="arrow_forward" size="xs" class="q-ml-xs" />
                </q-btn>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container q-mt-lg">
        <q-pagination v-model="currentPage" :max="totalPages" :max-pages="5" boundary-numbers direction-links @update:model-value="handlePageChange" class="justify-center" />
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// 分页相关
const currentPage = ref(1);
const pageSize = ref(5);
const totalBlogs = ref(0);

// 从URL获取页码
onMounted(() => {
  if (route.query.page) {
    currentPage.value = parseInt(route.query.page) || 1;
  }
  fetchBlogs();
});

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalBlogs.value / pageSize.value);
});

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
  router.push({
    path: '/blog/list',
    query: { page },
  });
  fetchBlogs();
};

// 模拟博客数据
const blogs = ref([
  {
    id: 1,
    title: '如何选择适合自己的海外代购商品',
    author: '购物专家',
    publishDate: '2023-06-15',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    summary: '在全球化的今天，海外代购已经成为了一种流行的购物方式。本文将为您介绍如何选择适合自己的海外代购商品，避免踩坑，获得最佳购物体验。',
    tags: ['购物指南', '海外代购', '选品技巧'],
    views: 1256,
  },
  {
    id: 2,
    title: '2023年最受欢迎的10大日本美妆产品',
    author: '美妆达人',
    publishDate: '2023-07-22',
    coverImage: 'https://cdn.quasar.dev/img/parallax2.jpg',
    summary: '日本美妆产品以其高品质和创新性在全球享有盛誉。本文盘点了2023年最受欢迎的10大日本美妆产品，从面霜到防晒，一应俱全。',
    tags: ['美妆', '日本产品', '热门榜单'],
    views: 3421,
  },
  {
    id: 3,
    title: '海外转运攻略：如何节省国际物流费用',
    author: '物流专家',
    publishDate: '2023-08-05',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    summary: '国际物流费用是海外购物的一大开支。本文分享了多种节省海外转运费用的方法和技巧，帮助您在享受全球购物的同时，降低物流成本。',
    tags: ['物流', '省钱技巧', '海外转运'],
    views: 2187,
  },
  {
    id: 4,
    title: '美国亚马逊购物指南：从注册到下单全流程',
    author: '跨境电商专家',
    publishDate: '2023-09-10',
    coverImage: 'https://cdn.quasar.dev/img/quasar.jpg',
    summary: '美国亚马逊是全球最大的电商平台之一，提供丰富的商品选择。本文详细介绍了如何在美国亚马逊上购物的全流程，包括账号注册、支付方式设置、商品筛选等。',
    tags: ['亚马逊', '海外购物', '教程'],
    views: 1893,
  },
  {
    id: 5,
    title: '如何辨别海外代购商品的真伪',
    author: '消费维权专家',
    publishDate: '2023-10-18',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    summary: '随着海外代购的普及，市场上假冒伪劣产品也层出不穷。本文教您如何辨别海外代购商品的真伪，保护自己的消费权益。',
    tags: ['真伪辨别', '消费维权', '海外代购'],
    views: 4562,
  },
  {
    id: 6,
    title: '欧洲奢侈品购物指南：最佳购物城市和季节',
    author: '奢侈品顾问',
    publishDate: '2023-11-05',
    coverImage: 'https://cdn.quasar.dev/img/parallax2.jpg',
    summary: '欧洲是奢侈品的发源地，也是购买奢侈品的理想之地。本文介绍了欧洲最佳的奢侈品购物城市，以及什么时候去购物最划算。',
    tags: ['奢侈品', '欧洲购物', '旅游购物'],
    views: 3105,
  },
  {
    id: 7,
    title: '韩国美食代购攻略：必买零食和调味料',
    author: '美食博主',
    publishDate: '2023-12-12',
    coverImage: 'https://cdn.quasar.dev/img/parallax1.jpg',
    summary: '韩国不仅有流行的美妆和时尚，还有令人垂涎的美食和零食。本文推荐了从韩国代购的必买零食和调味料，让您在家也能享受正宗韩味。',
    tags: ['韩国美食', '零食', '调味料'],
    views: 2756,
  },
  {
    id: 8,
    title: '海外购物退税指南：如何最大化您的退税收益',
    author: '财税顾问',
    publishDate: '2024-01-08',
    coverImage: 'https://cdn.quasar.dev/img/quasar.jpg',
    summary: '在许多国家，游客购物可以享受退税优惠。本文详细介绍了各国退税政策和流程，帮助您在海外购物时最大化退税收益。',
    tags: ['退税', '海外购物', '省钱技巧'],
    views: 1845,
  },
  {
    id: 9,
    title: '2024年值得关注的全球购物节日',
    author: '购物达人',
    publishDate: '2024-02-15',
    coverImage: 'https://cdn.quasar.dev/img/mountains.jpg',
    summary: '全球各地都有独特的购物节日，提供大幅折扣和优惠。本文盘点了2024年值得关注的全球购物节日，帮助您提前规划购物计划。',
    tags: ['购物节', '折扣', '全球购物'],
    views: 2134,
  },
  {
    id: 10,
    title: '如何处理海外代购中的物流延误和丢失问题',
    author: '客服专家',
    publishDate: '2024-03-20',
    coverImage: 'https://cdn.quasar.dev/img/parallax2.jpg',
    summary: '物流延误和包裹丢失是海外代购中常见的问题。本文提供了处理这些问题的实用建议和解决方案，帮助您应对各种物流意外。',
    tags: ['物流问题', '客服技巧', '海外代购'],
    views: 1678,
  },
]);

// 模拟API请求获取博客列表
const fetchBlogs = () => {
  // 这里是模拟数据，实际应用中应该调用API
  // 计算总博客数
  totalBlogs.value = blogs.value.length;

  // 在实际应用中，这里应该是API调用
  // const response = await fetch(`/api/blogs?page=${currentPage.value}&pageSize=${pageSize.value}`);
  // const data = await response.json();
  // blogs.value = data.items;
  // totalBlogs.value = data.total;
};

// 根据当前页码和页面大小计算要显示的博客
const displayedBlogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return blogs.value.slice(start, end);
});

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};
</script>

<style lang="scss" scoped>
.blog-list-page {
  padding: 20px 0;
}

.blog-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 30px;
  color: #333;
  text-align: center;
}

.blog-item {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 16px;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
}

.blog-image {
  border-radius: 6px;
  height: 100%;
  min-height: 200px;
}

.blog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.blog-title {
  font-size: 20px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 10px;
  line-height: 1.3;
}

.blog-link {
  color: #333;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: #1976d2;
  }
}

.blog-meta {
  font-size: 13px;
  color: #666;
}

.blog-summary {
  flex-grow: 1;
  margin: 10px 0;
  color: #555;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.blog-tags {
  margin-top: auto;
}

.read-more-btn {
  align-self: flex-start;
  padding: 8px 12px;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px;
}

@media (max-width: 767px) {
  .page-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .blog-item {
    padding: 12px;
  }

  .blog-image {
    min-height: 180px;
    margin-bottom: 12px;
  }

  .blog-title {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .blog-summary {
    -webkit-line-clamp: 2;
    line-clamp: 2;
    margin: 8px 0;
  }

  .blog-meta {
    font-size: 12px;
  }

  .pagination-container {
    margin: 30px 0 15px;
  }
}
</style>
