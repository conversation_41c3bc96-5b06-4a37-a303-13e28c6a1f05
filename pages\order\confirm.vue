<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="orderConfirm-page q-pa-md">
    <div class="row justify-between items-center text-center bg-white q-py-md">
      <div class="text-h5 q-ml-lg text-primary text-weight-medium">订单确认</div>
      <!-- 在PC视图下显示进度条，在移动端隐藏 -->
      <StepProgressSimple class="step-progress hide-on-mobile" currentStep="1" />
    </div>
    <!-- 商品信息 -->
    <div class="product-info q-mt-lg">
      <!-- 表格标题行 - PC视图 -->
      <div class="row items-center q-py-sm bg-blue-1 text-blue-9 table-header rounded-borders hide-on-mobile">
        <div class="col-sm-4 text-center text-weight-medium">商品信息</div>
        <div class="col-sm-2 text-center text-weight-medium">备注</div>
        <div class="col-sm-2 text-center text-weight-medium">服务</div>
        <div class="col-sm-1 text-center text-weight-medium">单价</div>
        <div class="col-sm-1 text-center text-weight-medium">数量</div>
        <div class="col-sm-2 text-center text-weight-medium">金额</div>
      </div>

      <!-- 表格标题行 - 移动端视图 -->
      <div class="row items-center q-py-sm bg-blue-1 text-blue-9 table-header rounded-borders mobile-only-block">
        <div class="row no-wrap w-100">
          <div class="col-12 text-center text-weight-medium">商品信息</div>
        </div>
      </div>

      <!-- 店铺分组展示商品 -->
      <ShopGroup v-for="(shop, shopIndex) in cartList" :key="shopIndex" :shop="shop" :free-services="freeServeList" :charge-services="chargeServeList" @update:services="updateItemServices" />
    </div>
    <!-- //选择国家 -->
    <CountrySelector v-model="country" :disable-country="disableCountry" />

    <!-- //优惠信息 -->
    <CouponSelector v-model="couponId" :options="couponOptions" @update:model-value="onSelectCoupon" />
    <q-separator class="q-mt-lg" />
    <!-- //核算 -->
    <div class="row justify-end q-mt-md price-summary">
      <!-- PC端视图 - 重新组织结构，每行标题和金额放在同一个容器中 -->
      <div class="hide-on-mobile row justify-end q-py-md text-subtitle1 w-100">
        <div class="col-sm-6 col-md-4">
          <!-- 商品费用行 -->
          <div class="row no-wrap q-py-xs align-center">
            <div class="col-6 text-right q-pr-md text-grey-8">商品费用：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.totalPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 运费行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.deliveryPrice > 0">
            <div class="col-6 text-right q-pr-md text-grey-8">运费：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.deliveryPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 增值服务行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.servicePrice > 0">
            <div class="col-6 text-right q-pr-md text-grey-8">增值服务：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.servicePrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 平台佣金行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.platformPrice > 0">
            <div class="col-6 text-right q-pr-md text-grey-8">平台佣金：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.platformPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 优惠券行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.couponPrice > 0">
            <div class="col-6 text-right q-pr-md text-grey-8">优惠券：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red price-amount primary-currency" v-html="'- ' + formatAmount(state.orderInfo.price.couponPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <q-separator class="q-mt-sm" />

          <!-- 应付金额行 -->
          <div class="row no-wrap q-py-xs align-center q-mt-sm">
            <div class="col-6 text-right q-pr-md text-subtitle1 text-weight-medium">应付金额：</div>
            <div class="col-6 text-right">
              <div class="text-h6 text-bold text-red price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.payPrice, { allowWrap: false })"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动端视图 - 更紧凑的布局，同样重新组织结构 -->
      <div class="mobile-only-block w-100">
        <div class="q-py-xs mobile-price-summary">
          <!-- 商品费用行 -->
          <div class="row no-wrap q-py-xs align-center">
            <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">商品费用：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.totalPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 运费行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.deliveryPrice > 0">
            <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">运费：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.deliveryPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 增值服务行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.servicePrice > 0">
            <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">增值服务：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.servicePrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 平台佣金行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.platformPrice > 0">
            <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">平台佣金：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.platformPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <!-- 优惠券行 -->
          <div class="row no-wrap q-py-xs align-center" v-if="state.orderInfo.price.couponPrice > 0">
            <div class="col-6 text-right q-pr-xs text-grey-8 text-caption">优惠券：</div>
            <div class="col-6 text-right">
              <div class="text-bold text-red text-caption mobile-price-amount primary-currency" v-html="'- ' + formatAmount(state.orderInfo.price.couponPrice, { allowWrap: false })"></div>
            </div>
          </div>

          <q-separator class="q-mt-xs" />

          <!-- 应付金额行 -->
          <div class="row no-wrap q-py-xs align-center q-mt-xs">
            <div class="col-6 text-right q-pr-xs text-weight-medium">应付金额：</div>
            <div class="col-6 text-right">
              <div class="text-subtitle1 text-bold text-red mobile-price-amount primary-currency" v-html="formatAmount(state.orderInfo.price.payPrice, { allowWrap: false })"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- //提交 -->
    <div>
      <!-- PC端视图 -->
      <div class="hide-on-mobile row justify-end q-mt-lg q-mb-lg action-buttons">
        <q-btn outline class="q-mr-md return-cart-btn" color="primary" @click="returnToCart">
          <q-icon name="arrow_back" size="18px" class="q-mr-sm" />
          <span>返回购物车</span>
        </q-btn>
        <q-btn unelevated size="lg" class="q-px-xl submit-btn" color="primary" @click="onConfirm">
          <span class="text-weight-medium">提交订单</span>
          <q-icon name="check_circle" size="20px" class="q-ml-sm" />
        </q-btn>
      </div>

      <!-- 移动端视图 - 更紧凑的布局 -->
      <div class="mobile-only-block">
        <div class="row justify-between q-mt-sm q-mb-md mobile-action-buttons">
          <q-btn outline dense class="return-cart-btn-mobile" color="primary" @click="returnToCart">
            <q-icon name="arrow_back" size="14px" class="q-mr-xs" />
            <span class="text-caption">返回购物车</span>
          </q-btn>
          <q-btn unelevated dense class="submit-btn-mobile" color="primary" @click="onConfirm">
            <span class="text-weight-medium">提交订单</span>
            <q-icon name="check_circle" size="16px" class="q-ml-xs" />
          </q-btn>
        </div>
      </div>
    </div>
  </div>
  <Footer />
</template>

<script setup>
import { useOrderConfirmStore } from '~/store/orderConfirm';
import { useCartStore } from '~/store/cart';
import StepProgressSimple from '~/components/widgets/StepProgressSimple.vue';
import CartApi from '@/composables/cartApi';
import OrderApi from '@/composables/orderApi';
import { useServeStore } from '../../store/serve';
import { computed } from 'vue';
import { useCurrencyStore } from '~/store/currency';
import { useCurrency } from '~/composables/useCurrency';

// 导入组件
import ShopGroup from '~/components/order/ShopGroup.vue';
import CountrySelector from '~/components/form/CountrySelector.vue';
import CouponSelector from '~/components/form/CouponSelector.vue';
import { PayOrderSources } from '../../utils/constants';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const breadcrumbs = [{ label: '订单确认', to: '/order/confirm' }];

const { formatAmount } = useCurrency();

const cartList = ref([]);
const isInitializing = ref(true); // 添加初始化标志
const isProcessingRequest = ref(false); // 添加请求处理标志，防止重复请求

const orderConfirmStore = useOrderConfirmStore();
const cartStore = useCartStore();
const serveStore = useServeStore();
const currencyStore = useCurrencyStore();

const country = ref('');
const disableCountry = ref('us');

const state = reactive({
  orderPayload: {},
  orderInfo: {
    items: [], // 商品项列表
    price: {}, // 价格信息
  },

  couponInfo: [], // 优惠劵列表
  showDiscount: false, // 是否展示营销活动
  pointStatus: false, //是否使用积分
});

const addressState = ref({
  addressInfo: {}, // 选择的收货地址
  deliveryType: 1, // 收货方式：1-快递配送，2-门店自提
  isPickUp: true, // 门店自提是否开启
  pickUpInfo: {}, // 选择的自提门店信息
  receiverName: '', // 收件人名称
  receiverMobile: '', // 收件人手机
});
// 免费服务
const freeServeList = computed(() => {
  return serveStore.list.filter((item) => item.type === 10);
});
// 收费服务
const chargeServeList = computed(() => {
  return serveStore.list.filter((item) => item.type === 20);
});

const couponId = ref(null);

const cartItem = ref([]);

// 动态生成优惠券选项
const couponOptions = computed(() => {
  if (state.couponInfo.length > 0) {
    // 有可用优惠券，添加“不使用优惠券”选项
    const options = [{ id: null, name: '不使用优惠券' }, ...state.couponInfo];
    return options;
  } else {
    // 无可用优惠券，添加“无可用优惠券”选项
    const options = [{ id: null, name: '无可用优惠券' }];
    return options;
  }
});

// 选择优惠券
async function onSelectCoupon() {
  state.orderPayload.couponId = couponId.value;

  // 确保不是在初始化过程中
  if (!isInitializing.value) {
    await getOrderInfo();
  }
}

onMounted(async () => {
  console.log('orderConfirm - initialization started');
  isInitializing.value = true; // 确保初始化标志为true

  //获取服务列表
  serveStore.fetchServices();
  //根据购物车ids获取需要结算的购物车列表详情
  const selectIds = orderConfirmStore.selectIds;
  if (selectIds && selectIds.length > 0) {
    console.log('slectProduct:', selectIds);
    const res = await CartApi.getCartListByIds(selectIds);
    if (res.code === 0) {
      orderConfirmStore.list = res.data.validList;
      cartList.value = formatCartData(orderConfirmStore.list);
      cartItem.value = orderConfirmStore.list.map((item) => ({
        skuId: item.sku.id, // 从 sku 获取 ID
        count: item.count, // 购买数量
        cartId: item.id, // 购物车 ID
        freeServices: [], // 初始化免费服务列表为空数组
        chargeServices: [], // 初始化收费服务列表为空数组
      }));

      // 初始化每个商品的selectedServices属性
      orderConfirmStore.list.forEach((item) => {
        if (!item.selectedServices) {
          item.selectedServices = [];
        }
      });

      // 预计算订单 - 直接调用一次
      await getOrderInfo();

      // 初始化完成后，将标志设置为false
      console.log('orderConfirm - initialization completed');
      isInitializing.value = false;
    }
  } else {
    navigateTo('/');
  }
});

// 提交订单
const onConfirm = () => {
  if (!country.value) {
    useNuxtApp().$showNotify({ msg: '请选择收货国家', type: 'negative' });
    return;
  }

  submitOrder();
};
const submitOrder = async () => {
  // 我们不需要准备全局服务数组，因为API期望的是每个商品项的服务

  // 确保每个购物车项都有freeServices和chargeServices属性
  cartItem.value.forEach((item) => {
    const cartItemIndex = orderConfirmStore.list.findIndex((ci) => ci.id === item.cartId);
    if (cartItemIndex >= 0) {
      const cartItemObj = orderConfirmStore.list[cartItemIndex];

      // 初始化服务数组
      item.freeServices = [];
      item.chargeServices = [];

      // 如果有选择服务，则添加到对应数组
      if (cartItemObj.selectedServices && cartItemObj.selectedServices.length > 0) {
        cartItemObj.selectedServices.forEach((service) => {
          if (service.type === 10) {
            item.freeServices.push(service.id);
          } else {
            item.chargeServices.push(service.id);
          }
        });
      }
    }
  });

  const { code, data } = await OrderApi.createOrder({
    items: cartItem.value,
    couponId: state.orderPayload.couponId,
    remark: state.orderPayload.remark,
    deliveryType: 1, //默认快递运输
    pointStatus: false,
    combinationActivityId: state.orderPayload.combinationActivityId,
    combinationHeadId: state.orderPayload.combinationHeadId,
    seckillActivityId: state.orderPayload.seckillActivityId,
    pointActivityId: state.orderPayload.pointActivityId,
    currency: currencyStore.getSelectedCurrency.currency,
  });
  if (code !== 0) {
    return;
  }
  //从本地购物车删除已下单项
  if (cartItem.value.length > 0) {
    cartStore.removeCartItemLocal(cartItem.value);
    orderConfirmStore.selectIds = null;
  }

  if (data.payOrderId && data.payOrderId > 0) {
    // sessionStorage.setItem('payOrderId', data.payOrderId);
    // navigateTo('/pay');
    navigateTo({
      path: '/pay',
      query: { pId: data.payOrderId, ps: PayOrderSources.ORDER },
    });
  } else {
    navigateTo('/account/orders', {
      params: {
        id: data.id,
      },
    });
  }
};

// 检查库存 & 计算订单价格
async function getOrderInfo() {
  console.log('getOrderInfo called, isInitializing:', isInitializing.value, 'isProcessingRequest:', isProcessingRequest.value);

  // 如果已经在处理请求，则跳过
  if (isProcessingRequest.value) {
    console.log('Skipping duplicate request');
    return;
  }

  // 设置请求处理标志
  isProcessingRequest.value = true;

  try {
    // 确保每个购物车项都有freeServices和chargeServices属性
    cartItem.value.forEach((item) => {
      const cartItemIndex = orderConfirmStore.list.findIndex((ci) => ci.id === item.cartId);
      if (cartItemIndex >= 0) {
        const cartItemObj = orderConfirmStore.list[cartItemIndex];

        // 初始化服务数组
        item.freeServices = [];
        item.chargeServices = [];

        // 如果有选择服务，则添加到对应数组
        if (cartItemObj.selectedServices && cartItemObj.selectedServices.length > 0) {
          cartItemObj.selectedServices.forEach((service) => {
            const serviceObj = {
              id: service.id,
            };

            if (service.type === 10) {
              item.freeServices.push(serviceObj);
            } else {
              item.chargeServices.push(serviceObj);
            }
          });
        }
      }
    });

    // 计算价格
    const { data, code } = await OrderApi.settlementOrder({
      items: cartItem.value,
      couponId: state.orderPayload.couponId,
      deliveryType: 1,
      pointStatus: false, //是否使用积分
    });

    if (code !== 0) {
      return;
    }

    state.orderInfo = data;
    state.couponInfo = data.coupons || [];

    // 设置收货地址
    if (state.orderInfo.address) {
      addressState.value.addressInfo = state.orderInfo.address;
    }
  } catch (error) {
    console.error('Error in getOrderInfo:', error);
  } finally {
    // 无论成功还是失败，都重置请求处理标志
    isProcessingRequest.value = false;
  }
}

const returnToCart = () => {
  navigateTo('/cart');
};

//格式化购物车数据按商店分组
function formatCartData(cartItems) {
  // 创建一个 Map 用于分组存储
  const groupedData = new Map();

  // 遍历所有购物车项
  cartItems.forEach((item) => {
    if (!item.spu.shopName) {
      item.spu.shopName = '未分组';
    }
    // 如果当前 shopName 不存在于 Map，则创建一个新分组
    if (!groupedData.has(item.spu.shopName)) {
      groupedData.set(item.spu.shopName, {
        shopName: item.spu.shopName || '未分组',
        items: [],
        freight: 0, // 初始化最大运费
      });
    }

    const shopGroup = groupedData.get(item.spu.shopName);

    // 更新最大运费
    shopGroup.freight = Math.max(shopGroup.freight, item.spu.freight || 0);

    // 将商品添加到对应分组的 items 数组中
    shopGroup.items.push(item);
  });

  // 转换 Map 为数组返回
  return Array.from(groupedData.values());
}

// 更新商品服务
const updateItemServices = (data) => {
  const { itemId, services } = data;

  // 遍历所有商品，找到对应的商品并更新服务
  cartList.value.forEach((shop) => {
    shop.items.forEach((item) => {
      if (item.id === itemId) {
        item.selectedServices = services;
      }
    });
  });
};

// 监听所有商品的服务选择变化
watch(
  () => cartList.value.map((shop) => shop.items.map((item) => item.selectedServices)),
  () => {
    console.log('watch triggered, isInitializing:', isInitializing.value);

    // 如果正在初始化，则跳过
    if (isInitializing.value) {
      console.log('Skipping watch during initialization');
      return;
    }

    // 遍历所有商品，更新cartItem中的服务信息
    cartList.value.forEach((shop) => {
      shop.items.forEach((item) => {
        const cartItemIndex = cartItem.value.findIndex((ci) => ci.cartId === item.id);
        if (cartItemIndex >= 0 && item.selectedServices) {
          // 初始化服务数组
          cartItem.value[cartItemIndex].freeServices = [];
          cartItem.value[cartItemIndex].chargeServices = [];

          // 如果有选择服务，则添加到对应数组
          if (item.selectedServices.length > 0) {
            item.selectedServices.forEach((service) => {
              const serviceObj = {
                id: service.id,
                code: service.code,
                price: service.price || 0,
              };

              if (service.free) {
                cartItem.value[cartItemIndex].freeServices.push(serviceObj);
              } else {
                cartItem.value[cartItemIndex].chargeServices.push(serviceObj);
              }
            });
          }
        }
      });
    });

    // 重新计算订单价格
    getOrderInfo();
  },
  { deep: true } // 移除immediate: false，使用isInitializing标志来控制
);

// 使用全局插件提供的方法
// const { $formatAmount } = useNuxtApp();

// function formatAmount(amount) {
//   return $formatAmount(amount);
// }
</script>

<style lang="scss" scoped>
.orderConfirm-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (max-width: 600px) {
    padding: 0 8px;
  }
}

// 表格标题行样式
.table-header {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// 店铺标题样式
.shop-header {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// 商品项样式
.product-item {
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f9f9f9;
  }

  .product-img {
    border: 1px solid #f0f0f0;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.05);
    }
  }

  .product-name {
    color: #333;
    font-weight: 500;
    text-decoration: none;

    &:hover {
      color: var(--q-primary);
      text-decoration: underline;
    }
  }

  .product-props {
    font-size: 12px;
  }
}

// 国家选择区域样式
.country-section {
  background-color: #f5f7fa;
  border: 1px solid #e0e6ed;
}

// 优惠券选择区域样式
.coupon-section {
  background-color: #fff8f0;
  border: 1px solid #ffe4cc;
}

// 提交按钮样式
.submit-btn {
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  min-width: 150px;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
}

// 返回购物车按钮样式
.return-cart-btn {
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(var(--q-primary), 0.1);
  }
}

// 步骤进度条样式
.step-progress {
  padding-right: 16px;
}

// 商品布局样式 - 响应式
.product-layout-mobile {
  display: flex;
  width: 100%;

  @media (min-width: 600px) {
    display: none;
  }
}

.product-layout-desktop {
  display: none;
  width: 100%;

  @media (min-width: 600px) {
    display: block;
  }

  .product-info-desktop {
    flex: 1;
    min-width: 0; // 防止flex子项溢出
  }
}

// 移动端专用样式
.mobile-only {
  display: none !important;

  @media (max-width: 599px) {
    display: flex !important;
  }
}

.mobile-only-block {
  display: none !important;

  @media (max-width: 599px) {
    display: block !important;
  }
}

// 防止文本换行
.nowrap {
  white-space: nowrap;
}

// 金额列样式
.amount-column {
  min-width: 140px; // 设置最小宽度，确保双币种显示不会换行
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 价格金额样式
.price-amount {
  min-width: 140px;
  display: inline-block;
  text-align: right;

  &.primary-currency {
    white-space: nowrap;

    :deep(.default-currency) {
      font-size: 0.8em;
      color: #666;
      opacity: 0.85;
      font-weight: normal;
      display: inline-block;
      margin-left: 4px;
      white-space: normal;
    }
  }
}

// 100%宽度
.w-100 {
  width: 100%;
}

// PC端专用样式
.hide-on-mobile {
  @media (max-width: 599px) {
    display: none !important;
  }
}

// 移动端服务选择样式
.selected-services-mobile {
  max-width: 100%;
  overflow-x: auto;
  padding-bottom: 4px;

  .service-chip-mobile {
    font-size: 11px;
    height: 24px;

    .q-icon {
      font-size: 12px;
    }
  }
}

// 移动端服务行样式
.selected-services-row {
  max-width: 100%;
  padding: 4px 0;
  border-top: 1px dashed #e0e0e0;

  .mobile-service-chip {
    font-size: 11px;
    height: 24px;
    margin-bottom: 4px;

    .q-icon {
      font-size: 12px;
    }
  }
}

// 移动端表头样式
@media (max-width: 599px) {
  .table-header {
    margin-bottom: 8px;
  }
}

// 移动端价格区域样式
.price-summary {
  @media (max-width: 599px) {
    justify-content: flex-end !important;

    .price-labels,
    .price-values {
      text-align: right;
    }
  }
}

// 移动端价格摘要样式
.mobile-price-summary {
  .text-caption {
    font-size: 12px;
    line-height: 1.2;
  }

  .q-py-xs {
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .q-mt-xs {
    margin-top: 4px;
  }

  .mobile-price-amount {
    min-width: 100px;
    display: inline-block;
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.primary-currency {
      white-space: nowrap;

      :deep(.default-currency) {
        font-size: 0.75em;
        color: #666;
        opacity: 0.85;
        font-weight: normal;
        display: inline-block;
        margin-left: 2px;
        white-space: normal;
      }
    }
  }
}

// 移动端按钮样式
.mobile-action-buttons {
  padding: 8px 0;

  .return-cart-btn-mobile {
    height: 32px;
    font-size: 12px;
    padding: 0 12px;
    border-radius: 4px;

    .q-icon {
      font-size: 14px;
    }
  }

  .submit-btn-mobile {
    height: 32px;
    font-size: 13px;
    padding: 0 16px;
    border-radius: 4px;
    font-weight: 500;

    .q-icon {
      font-size: 16px;
    }
  }
}

.border {
  border-top: 1px solid #e0e0e0;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}
.border:nth-last-child(1) {
  border-bottom: 1px solid #e0e0e0;
}

// 服务选择相关样式
.service-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-dropdown {
  min-width: 100px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f8ff;
    border-color: #1976d2;
  }
}

.service-btn-text {
  font-size: 12px;
  line-height: 1;
}

.service-chip {
  font-size: 13px;
  height: 28px;
  padding: 0 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

// 多行文本省略
.multiline-ellipsis,
.multiline-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.multiline-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 响应式布局样式
@media (max-width: 600px) {
  .wrap-on-mobile {
    flex-wrap: wrap;
  }

  .product-row {
    padding-left: 0 !important;
    margin-bottom: 12px;
  }

  .product-image {
    width: 80px;
  }

  .product-info-col {
    width: calc(100% - 80px);
  }

  .service-chip {
    font-size: 11px;
    height: 24px;
  }

  // 移动端卡片样式
  .product-card-mobile {
    width: 100%;

    .product-name {
      font-size: 13px;
      line-height: 1.4;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.87);
    }

    .product-props {
      font-size: 12px;
      line-height: 1.3;
    }

    .price-row {
      border-top: 1px dashed #e0e0e0;
      padding-top: 8px;

      .row.no-wrap {
        min-height: 24px;
      }

      .price-label {
        font-size: 12px;
        line-height: 1.2;
        font-weight: 500;
        white-space: nowrap;
      }

      .price-value {
        font-size: 13px;
        line-height: 1.4;
        font-weight: 500;
        white-space: nowrap;
      }
    }

    .service-row {
      border-top: 1px dashed #e0e0e0;
      padding-top: 8px;
    }

    .memo-row {
      border-top: 1px dashed #e0e0e0;
      padding-top: 8px;
    }

    .service-dropdown {
      height: 24px;
      min-height: 24px;
      padding: 0 8px;

      .service-btn-text {
        font-size: 12px;
        font-weight: normal;
      }
    }

    .service-chips-container {
      overflow: hidden;
    }

    .service-chips-wrapper {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }

    .mobile-service-chip {
      font-size: 11px;
      height: 24px;
      white-space: nowrap;

      /* 使用内联样式替代 */
    }

    .memo-label {
      font-size: 12px;
      line-height: 1.2;
      font-weight: 500;
    }

    .memo-content {
      font-size: 12px;
      line-height: 1.4;
    }
  }

  .country-selector {
    width: 100%;
    margin-left: 0 !important;
  }

  .coupon-select {
    width: 100%;
    margin-left: 0 !important;
  }

  .price-summary {
    margin-top: 16px;
  }

  .action-buttons {
    margin-top: 24px;
  }
}

@media (min-width: 601px) {
  .show-on-mobile {
    display: none !important;
  }

  .q-ml-md-desktop {
    margin-left: 16px;
  }

  .q-mb-sm-none {
    margin-bottom: 0;
  }

  .coupon-select {
    width: 300px;
  }
}
</style>
