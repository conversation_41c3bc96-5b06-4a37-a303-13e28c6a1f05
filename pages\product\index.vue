<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="product-detail-page q-pt-lg">
    <!-- 加载状态 -->
    <div v-if="searchStore.detailLoading" class="loading-container">
      <q-spinner color="primary" size="3em" />
      <p>正在获取商品信息...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="!searchStore.productDetail && !searchStore.detailLoading" class="error-container">
      <q-icon name="error_outline" size="60px" color="grey-5" />
      <h3>无法获取商品信息</h3>
      <p>请检查商品链接是否正确，或稍后重试</p>

      <div class="error-actions">
        <q-btn color="primary" @click="retrySearch" class="q-mr-sm">重试</q-btn>
        <q-btn flat color="grey" @click="goBack" class="q-mr-sm">返回</q-btn>
      </div>

      <!-- 自定义订单建议 -->
      <div class="custom-order-suggestion q-mt-lg">
        <q-card flat bordered class="suggestion-card">
          <q-card-section class="text-center">
            <q-icon name="build" size="32px" color="primary" class="q-mb-sm" />
            <h4 class="q-mb-sm">无法获取商品信息？</h4>
            <p class="q-mb-md">创建自定义订单，提供商品链接和详细信息，我们帮您代购！</p>
            <q-btn color="primary" @click="goToCustomOrderWithCurrentLink" icon="build"> 创建自定义订单 </q-btn>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 商品详情内容 -->
    <div v-else-if="searchStore.productDetail" class="product-detail-content">
      <!-- 主体布局 -->
      <div class="row q-col-gutter-md">
        <!-- 左侧：商品图片展示区 -->
        <div class="col-12 col-md-6 col-lg-5">
          <!-- 大图轮播 -->
          <div class="carousel-container" :class="{ 'mobile-carousel-container': isMobile }">
            <q-carousel
              v-model="currentSlide"
              transition-prev="slide-right"
              transition-next="slide-left"
              animated
              :arrows="!isMobile"
              swipeable
              :height="isMobile ? 'auto' : '500px'"
              class="rounded-borders product-main-image"
              :class="{ 'mobile-carousel': isMobile }">
              <q-carousel-slide v-for="(image, index) in productImages" :key="index" :name="index" class="column items-center justify-center q-pa-sm" :class="{ 'mobile-carousel-slide': isMobile }">
                <q-img :src="image" :alt="`商品图片 ${index + 1}`" class="full-width" :fit="isMobile ? 'cover' : 'contain'" :class="{ 'mobile-carousel-img': isMobile }" />
              </q-carousel-slide>
            </q-carousel>

            <!-- 移动端轮播按钮 -->
            <template v-if="isMobile">
              <q-btn round flat dense color="white" icon="chevron_left" class="mobile-carousel-nav mobile-carousel-nav-left" @click="prevSlide" />
              <q-btn round flat dense color="white" icon="chevron_right" class="mobile-carousel-nav mobile-carousel-nav-right" @click="nextSlide" />
            </template>
          </div>

          <!-- 小图预览区域 - 桌面端和平板端 -->
          <div class="thumbnail-carousel q-mt-sm gt-xs" v-if="productImages.length > 1">
            <div class="row justify-center q-gutter-sm">
              <q-img
                v-for="(image, index) in productImages.slice(0, 5)"
                :key="index"
                :src="image"
                :alt="`缩略图 ${index + 1}`"
                class="thumbnail-item rounded-borders"
                :class="{ active: currentSlide === index }"
                @click="currentSlide = index" />
            </div>
          </div>
        </div>

        <!-- 右侧：商品信息 -->
        <div class="col-12 col-md-6 col-lg-7 q-pa-md">
          <h1 class="product-title q-mb-md">{{ searchStore.productDetail.name }}</h1>

          <!-- 来源信息 -->
          <div class="source-info q-mb-md">
            <div class="row items-center q-gutter-sm">
              <q-chip color="primary" text-color="white" icon="store">
                {{ searchStore.productDetail.shopName || '官方店铺' }}
              </q-chip>
              <q-chip color="secondary" text-color="white" icon="language">
                {{ getPlatformName(searchStore.productDetail.source) }}
              </q-chip>
            </div>
            <div class="q-mt-sm">
              <a :href="searchStore.productDetail.sourceLink" target="_blank" class="text-primary">
                <q-icon name="open_in_new" size="sm" />
                查看原商品页面
              </a>
            </div>
          </div>

          <!-- 价格信息 -->
          <div class="price-section q-mt-sm q-py-sm rounded-borders">
            <div class="row items-center q-px-md">
              <div class="col-12 col-sm-2 q-pr-md text-center text-sm-right label-text">价格</div>
              <div class="col-12 col-sm-10 row items-center justify-center justify-sm-start q-mt-xs q-mt-sm-none" style="min-height: 32px">
                <span class="text-red text-bold text-h5"> ¥{{ (searchStore.productDetail.price / 100).toFixed(2) }} </span>
                <span v-if="searchStore.productDetail.marketPrice && searchStore.productDetail.marketPrice > searchStore.productDetail.price" class="text-grey-6 text-strike q-ml-sm">
                  ¥{{ (searchStore.productDetail.marketPrice / 100).toFixed(2) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 商品基本信息 -->
          <div class="product-info-container q-mt-md">
            <div class="row q-col-gutter-sm mobile-center">
              <div class="col-6 col-sm-4" v-if="searchStore.productDetail.stock">
                <div class="info-chip">
                  <q-icon name="inventory_2" size="xs" class="q-mr-xs" />
                  <span class="label-text q-mr-xs">库存:</span>
                  <span class="value-text">{{ searchStore.productDetail.stock }}</span>
                </div>
              </div>
              <div class="col-6 col-sm-4" v-if="searchStore.productDetail.salesCount">
                <div class="info-chip">
                  <q-icon name="trending_up" size="xs" class="q-mr-xs" />
                  <span class="label-text q-mr-xs">销量:</span>
                  <span class="value-text">{{ searchStore.productDetail.salesCount }}</span>
                </div>
              </div>
              <div class="col-6 col-sm-4" v-if="searchStore.productDetail.scores">
                <div class="info-chip">
                  <q-icon name="star" size="xs" class="q-mr-xs" />
                  <span class="label-text q-mr-xs">评分:</span>
                  <span class="value-text">{{ searchStore.productDetail.scores }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons q-mt-lg">
            <div class="row q-col-gutter-sm justify-center">
              <div class="col-12 col-sm-auto">
                <q-btn color="primary" size="lg" class="full-width action-btn" @click="startPurchase">
                  <q-icon name="shopping_cart" class="q-mr-xs" />
                  开始代购
                </q-btn>
              </div>
              <div class="col-12 col-sm-auto">
                <q-btn color="secondary" size="lg" outline class="full-width action-btn" @click="addToWishlist">
                  <q-icon name="favorite_border" class="q-mr-xs" />
                  收藏商品
                </q-btn>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品详情描述 -->
      <div class="product-description q-mt-xl" v-if="searchStore.productDetail.description">
        <q-card flat bordered>
          <q-card-section>
            <h3 class="text-h6 q-mb-md">商品详情</h3>
            <div v-html="searchStore.productDetail.description" class="product-desc-content"></div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>

  <Footer />
</template>

<script setup>
import { useSearchStore } from '~/store/search';
import { useRoute, useRouter } from 'vue-router';
import { useResponsive } from '~/composables/useResponsive';
import { getPlatformName } from '~/utils/searchUtils';

const route = useRoute();
const router = useRouter();
const searchStore = useSearchStore();
const { isMobile } = useResponsive();

const id = route.query.id;
const platform = route.query.platform;

// 轮播图相关
const currentSlide = ref(0);

// 面包屑导航
const breadcrumbs = [
  { label: '首页', to: '/' },
  { label: '商品详情', to: route.fullPath },
];

// 计算商品图片数组
const productImages = computed(() => {
  if (!searchStore.productDetail) return [];

  const images = [];

  // 添加主图
  if (searchStore.productDetail.picUrl) {
    images.push(searchStore.productDetail.picUrl);
  }

  // 添加轮播图
  if (searchStore.productDetail.sliderPicUrls && Array.isArray(searchStore.productDetail.sliderPicUrls)) {
    images.push(...searchStore.productDetail.sliderPicUrls);
  }

  return images;
});

// 轮播控制函数
const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  } else {
    currentSlide.value = productImages.value.length - 1;
  }
};

const nextSlide = () => {
  if (currentSlide.value < productImages.value.length - 1) {
    currentSlide.value++;
  } else {
    currentSlide.value = 0;
  }
};

// 页面初始化
onMounted(async () => {
  // const productUrl = route.query.url;

  // if (!productUrl) {
  //   router.push('/search');
  //   return;
  // }
  if (id == null || platform == null) {
    console.log('id or platform is null');
    router.push('/search');
  }

  // 执行商品详情搜索
  // await searchStore.searchProductDetail(productUrl);
  await searchStore.searchProductDetailById(id, platform);
});

// 重试搜索
const retrySearch = async () => {
  const productUrl = route.query.url;
  if (productUrl) {
    await searchStore.searchProductDetail(productUrl);
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 开始代购
const startPurchase = () => {
  // 这里可以跳转到代购流程页面
  console.log('开始代购');
};

// 添加到收藏
const addToWishlist = () => {
  // 这里可以添加到收藏夹
  console.log('添加到收藏');
};

// 跳转到自定义订单页面并预填充当前商品链接
const goToCustomOrderWithCurrentLink = () => {
  const productUrl = route.query.url;
  if (productUrl) {
    const params = new URLSearchParams();
    params.set('productLink', productUrl);
    router.push(`/diy-order?${params.toString()}`);
  } else {
    router.push('/diy-order');
  }
};
</script>

<style lang="scss" scoped>
.product-detail-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  text-align: center;

  h3 {
    margin: 20px 0 10px;
    color: #333;
  }

  p {
    margin-bottom: 20px;
    color: #666;
  }

  .error-actions {
    margin-bottom: 20px;
  }

  .custom-order-suggestion {
    width: 100%;
    max-width: 400px;

    .suggestion-card {
      border: 2px dashed #0073e6;

      h4 {
        color: #333;
        margin: 0;
      }

      p {
        color: #666;
        margin: 10px 0;
        line-height: 1.5;
      }
    }
  }
}

.product-detail-content {
  .carousel-container {
    position: relative;

    .mobile-carousel-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      background: rgba(0, 0, 0, 0.5);

      &.mobile-carousel-nav-left {
        left: 10px;
      }

      &.mobile-carousel-nav-right {
        right: 10px;
      }
    }
  }

  .thumbnail-item {
    width: 80px;
    height: 80px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;

    &.active {
      border-color: #0073e6;
    }

    &:hover {
      border-color: #0073e6;
    }
  }

  .product-title {
    font-size: 24px;
    font-weight: bold;
    line-height: 1.4;
    color: #333;
  }

  .source-info {
    .q-chip {
      font-size: 12px;
    }
  }

  .price-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;

    .label-text {
      font-weight: 600;
      color: #666;
    }
  }

  .info-chip {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;

    .label-text {
      color: #666;
    }

    .value-text {
      color: #333;
      font-weight: 500;
    }
  }

  .action-btn {
    min-height: 48px;
    font-size: 16px;
    font-weight: 600;
  }

  .product-desc-content {
    line-height: 1.6;

    img {
      max-width: 100%;
      height: auto;
    }
  }
}

@media (max-width: 599px) {
  .product-detail-page {
    padding: 15px 10px;
  }

  .product-title {
    font-size: 20px;
  }

  .mobile-center {
    text-align: center;
  }
}
</style>
