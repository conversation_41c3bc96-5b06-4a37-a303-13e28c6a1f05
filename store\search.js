import { defineStore } from 'pinia';
import SearchApi from '~/composables/searchApi';
import { analyzeSearchInput, PLATFORMS } from '~/utils/searchUtils';

export const useSearchStore = defineStore({
  id: 'search',
  state: () => ({
    // 关键字搜索相关状态
    searchResults: [], // 搜索结果列表
    searchQuery: '', // 搜索关键词
    loading: false, // 加载状态
    total: 0, // 总记录数
    pageSize: 10, // 每页大小
    currentPage: 1, // 当前页码

    // 平台和筛选相关状态
    selectedPlatform: PLATFORMS.TAOBAO, // 当前选中的平台
    sortBy: 'default', // 排序方式
    priceMin: null, // 最低价格（分）
    priceMax: null, // 最高价格（分）

    // 商品详情搜索相关状态
    productDetail: null, // 通过链接搜索的商品详情
    detailLoading: false, // 详情加载状态

    // 搜索类型和历史
    searchType: 'keyword', // 搜索类型：'keyword' | 'url'
    searchHistory: [], // 搜索历史
  }),
  actions: {
    // 设置搜索关键词
    setSearchQuery(query) {
      this.searchQuery = query;
      this.searchType = 'keyword';
    },

    // 设置当前页码
    setCurrentPage(page) {
      this.currentPage = page;
    },

    // 设置每页大小
    setPageSize(size) {
      this.pageSize = size;
    },

    // 设置选中的平台
    setSelectedPlatform(platform) {
      this.selectedPlatform = platform;
    },

    // 设置排序方式
    setSortBy(sortBy) {
      this.sortBy = sortBy;
    },

    // 设置价格范围
    setPriceRange(min, max) {
      this.priceMin = min;
      this.priceMax = max;
    },

    // 分析搜索输入并设置相应状态
    analyzeAndSetSearch(input) {
      const analysis = analyzeSearchInput(input);

      if (analysis.type === 'url') {
        this.searchType = 'url';
        this.searchQuery = analysis.input;
        if (analysis.platform) {
          this.selectedPlatform = analysis.platform;
        }
      } else {
        this.searchType = 'keyword';
        this.searchQuery = analysis.input;
      }

      return analysis;
    },

    // 执行关键字搜索
    async searchProducts(page = this.currentPage, pageSize = this.pageSize) {
      this.loading = true;

      try {
        const params = {
          keyword: this.searchQuery,
          platform: this.selectedPlatform,
          pageNo: page,
          pageSize: pageSize,
          sort: this.sortBy,
        };

        // 添加价格筛选参数
        if (this.priceMin !== null) {
          params.priceMin = this.priceMin;
        }
        if (this.priceMax !== null) {
          params.priceMax = this.priceMax;
        }

        const response = await SearchApi.searchProducts(params);

        if (response.code === 0) {
          this.searchResults = response.data.list || [];
          this.total = response.data.total || 0;
          this.currentPage = page;
          this.pageSize = pageSize;

          // 添加到搜索历史
          this.addToSearchHistory(this.searchQuery, 'keyword');
        } else {
          console.error('搜索失败:', response.msg);
          this.searchResults = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('搜索出错:', error);
        this.searchResults = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }

      return {
        results: this.searchResults,
        total: this.total,
      };
    },

    // 执行商品ID搜索
    async searchProductDetailById(id, platform) {
      this.detailLoading = true;
      this.searchType = 'url';

      try {
        const response = await SearchApi.searchDetailById(id, platform);

        if (response.code === 0) {
          this.productDetail = response.data;

          // 添加到搜索历史
          this.addToSearchHistory(id, 'url');
        } else {
          console.error('商品详情搜索失败:', response.msg);
          this.productDetail = null;
        }
      } catch (error) {
        console.error('商品详情搜索出错:', error);
        this.productDetail = null;
      } finally {
        this.detailLoading = false;
      }

      return this.productDetail;
    },

    // 执行商品链接搜索
    async searchProductDetail(productUrl) {
      this.detailLoading = true;
      this.searchType = 'url';

      try {
        const response = await SearchApi.searchDetail(productUrl);

        if (response.code === 0) {
          this.productDetail = response.data;

          // 添加到搜索历史
          this.addToSearchHistory(productUrl, 'url');
        } else {
          console.error('商品详情搜索失败:', response.msg);
          this.productDetail = null;
        }
      } catch (error) {
        console.error('商品详情搜索出错:', error);
        this.productDetail = null;
      } finally {
        this.detailLoading = false;
      }

      return this.productDetail;
    },

    // 添加到搜索历史
    addToSearchHistory(query, type) {
      const historyItem = {
        query,
        type,
        timestamp: Date.now(),
      };

      // 移除重复项
      this.searchHistory = this.searchHistory.filter((item) => !(item.query === query && item.type === type));

      // 添加到开头
      this.searchHistory.unshift(historyItem);

      // 限制历史记录数量
      if (this.searchHistory.length > 20) {
        this.searchHistory = this.searchHistory.slice(0, 20);
      }
    },

    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = [];
      this.total = 0;
    },

    // 清空商品详情
    clearProductDetail() {
      this.productDetail = null;
    },

    // 重置搜索状态
    resetSearchState() {
      this.searchQuery = '';
      this.searchResults = [];
      this.total = 0;
      this.currentPage = 1;
      this.productDetail = null;
      this.searchType = 'keyword';
    },
  },
  getters: {
    // 获取总页数
    totalPages: (state) => {
      return Math.ceil(state.total / state.pageSize);
    },

    // 判断是否有搜索结果
    hasResults: (state) => {
      return state.searchResults.length > 0;
    },

    // 判断是否为空搜索
    isEmptySearch: (state) => {
      return !state.searchQuery || state.searchQuery.trim() === '';
    },

    // 判断是否为关键字搜索
    isKeywordSearch: (state) => {
      return state.searchType === 'keyword';
    },

    // 判断是否为链接搜索
    isUrlSearch: (state) => {
      return state.searchType === 'url';
    },

    // 获取当前平台名称
    currentPlatformName: (state) => {
      const platformNames = {
        [PLATFORMS.TAOBAO]: '淘宝',
        [PLATFORMS.TMALL]: '天猫',
        [PLATFORMS.ALIBABA_1688]: '1688',
        [PLATFORMS.JD]: '京东',
      };
      return platformNames[state.selectedPlatform] || state.selectedPlatform;
    },

    // 获取搜索参数对象
    searchParams: (state) => {
      return {
        keyword: state.searchQuery,
        platform: state.selectedPlatform,
        pageNo: state.currentPage,
        pageSize: state.pageSize,
        sort: state.sortBy,
        priceMin: state.priceMin,
        priceMax: state.priceMax,
      };
    },

    // 获取最近的搜索历史
    recentSearchHistory: (state) => {
      return state.searchHistory.slice(0, 10);
    },
  },
});
