<template>
  <div class="wishlist-page">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="favorite" size="xs" color="pink" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('wishlist.title') }}</span>
      </div>
    </div>

    <!-- 商品列表区域 -->
    <div class="q-pa-md">
      <!-- 加载中 -->
      <div v-if="loading" class="column items-center q-py-xl">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-sm text-grey-7">{{ $t('wishlist.loading') }}</div>
      </div>

      <!-- 无数据 -->
      <div v-else-if="products.length === 0" class="column items-center q-py-xl">
        <q-icon name="favorite_border" size="4em" color="grey-5" />
        <div class="text-grey-7 q-mt-sm">{{ $t('wishlist.noProducts') }}</div>
        <q-btn color="primary" :label="$t('wishlist.browseProducts')" class="q-mt-md" to="/products" />
      </div>

      <!-- 商品卡片列表 -->
      <div v-else>
        <div class="row q-col-gutter-md">
          <div v-for="product in products" :key="product.id" class="col-6 col-sm-6 col-md-4 col-lg-3 q-mb-md">
            <ProductCard :product="product" @remove="confirmRemove" />
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="row justify-between items-center q-mt-lg flex-wrap">
          <div class="col-12 col-sm-auto q-mb-sm-none q-mb-sm">
            <div class="row justify-end justify-sm-start">
              <div class="text-caption text-grey-8">
                {{
                  $t('wishlist.pagination', {
                    total: total,
                    current: pagination.page,
                    pages: Math.ceil(total / pagination.pageSize) || 1,
                  })
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-auto">
            <div class="row justify-center">
              <q-pagination
                v-model="pagination.page"
                :max="Math.ceil(total / pagination.pageSize)"
                :max-pages="$q.screen.lt.sm ? 3 : 6"
                boundary-links
                direction-links
                @update:model-value="onPageChange"
                class="pagination-control" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="confirmDialog" persistent>
      <q-card class="confirm-dialog">
        <q-card-section class="row items-center">
          <q-avatar icon="help_outline" color="pink-6" text-color="white" />
          <span class="q-ml-sm text-body1">{{ $t('wishlist.confirmRemove.title') }}</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('wishlist.confirmRemove.cancel')" color="grey-7" v-close-popup />
          <q-btn flat :label="$t('wishlist.confirmRemove.confirm')" color="negative" @click="removeProduct" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import ProductCard from '~/components/account/ProductCard.vue';

// 初始化国际化
useI18n();

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();

// 商品列表
const products = ref([]);
const total = ref(0);
const loading = ref(false);

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 12,
});

// 删除确认对话框
const confirmDialog = ref(false);
const productToRemove = ref(null);

// 页面加载时获取数据
onMounted(() => {
  fetchWishlist();
});

// 获取收藏列表
async function fetchWishlist() {
  loading.value = true;
  try {
    // 实际应用中，这里应该调用API获取收藏列表
    // 这里使用模拟数据
    setTimeout(() => {
      const mockProducts = [
        {
          id: 1,
          name: '高端智能手机，超长续航，强动力处理器，高清大屏幕',
          price: 4999,
          image: 'https://cdn.quasar.dev/img/mountains.jpg',
        },
        {
          id: 2,
          name: '超薄笔记本电脑',
          price: 6999,
          image: 'https://cdn.quasar.dev/img/parallax2.jpg',
        },
        {
          id: 3,
          name: '无线降噪耳机',
          price: 1299,
          image: 'https://cdn.quasar.dev/img/parallax1.jpg',
        },
        {
          id: 4,
          name: '智能手表',
          price: 1599,
          image: 'https://cdn.quasar.dev/img/quasar.jpg',
        },
        {
          id: 5,
          name: '家用智能音箱',
          price: 899,
          image: 'https://cdn.quasar.dev/img/blueish.jpg',
        },
        {
          id: 6,
          name: '高清摄像机',
          price: 5999,
          image: 'https://cdn.quasar.dev/img/parallax1.jpg',
        },
        {
          id: 7,
          name: '游戏手柄',
          price: 399,
          image: 'https://cdn.quasar.dev/img/mountains.jpg',
        },
        {
          id: 8,
          name: '机械键盘',
          price: 499,
          image: 'https://cdn.quasar.dev/img/parallax2.jpg',
        },
      ];

      // 模拟分页
      const start = (pagination.page - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      products.value = mockProducts.slice(start, end);
      total.value = mockProducts.length;
      loading.value = false;
    }, 800);
  } catch (error) {
    console.error('获取收藏列表失败', error);
    $q.notify({
      color: 'negative',
      message: '获取收藏列表失败',
      icon: 'error',
    });
    loading.value = false;
  }
}

// 分页变更事件
function onPageChange() {
  fetchWishlist();
}

// 确认移除商品
function confirmRemove(productId) {
  productToRemove.value = productId;
  confirmDialog.value = true;
}

// 移除商品
function removeProduct() {
  if (!productToRemove.value) return;

  // 实际应用中，这里应该调用API移除收藏商品
  // 这里使用模拟删除
  products.value = products.value.filter((item) => item.id !== productToRemove.value);

  // 如果当前页没有数据了，则返回上一页
  if (products.value.length === 0 && pagination.page > 1) {
    pagination.page--;
    fetchWishlist();
  }

  // 更新总数
  total.value--;

  $q.notify({
    color: 'positive',
    message: $t('wishlist.removeSuccess'),
    icon: 'check_circle',
  });

  productToRemove.value = null;
}
</script>

<style lang="scss" scoped>
.wishlist-page {
  .q-pagination {
    .q-btn {
      font-weight: 500;
      padding: 0 8px;
      min-height: 32px;
    }
  }

  .confirm-dialog {
    border-radius: 8px;
    max-width: 350px;
  }

  // 响应式调整
  @media (max-width: 599px) {
    .q-col-gutter-md > .col-6 {
      padding: 4px 8px;
    }

    .pagination-control {
      .q-btn {
        padding: 0 6px;
        min-height: 28px;
        font-size: 12px;
      }
    }
  }
}
</style>
