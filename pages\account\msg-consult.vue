<template>
  <div class="message-consult">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="question_answer" size="xs" color="orange-6" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('messageCenter.consult.pageTitle') }}</span>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section q-pa-md">
      <div class="row q-col-gutter-md items-center">
        <div class="col-12 col-sm-auto">
          <q-select v-model="filter.type" :options="consultTypeOptions" :label="$t('messageCenter.consult.consultType')" outlined dense emit-value map-options options-dense class="consult-filter" />
        </div>
        <div class="col-12 col-sm-auto">
          <q-select v-model="filter.status" :options="statusOptions" :label="$t('messageCenter.consult.statusFilter')" outlined dense emit-value map-options options-dense class="consult-filter" />
        </div>
        <div class="col-12 col-sm-auto">
          <DateRangePicker v-model="filter.dateRange" :label="$t('messageCenter.consult.dateRange')" @update:model-value="loadConsults" />
        </div>
        <div class="col-12 col-sm-auto">
          <q-input v-model="filter.keyword" outlined dense :placeholder="$t('messageCenter.consult.searchPlaceholder')" class="consult-search">
            <template v-slot:append>
              <q-icon name="search" @click="loadConsults" class="cursor-pointer" />
            </template>
          </q-input>
        </div>
        <div class="col-12 col-sm-auto q-gutter-x-sm">
          <q-btn color="primary" :label="$t('messageCenter.consult.newConsult')" icon="add" @click="openNewConsultDialog" />
        </div>
      </div>
    </div>

    <!-- 咨询列表 -->
    <div class="consult-list q-pa-md">
      <!-- PC端表格视图 -->
      <div v-if="!$q.screen.lt.sm" class="desktop-view">
        <q-table
          :rows="consults"
          :columns="columns"
          row-key="id"
          :pagination="pagination"
          :loading="loading"
          :rows-per-page-options="[10, 20, 50]"
          @request="onRequest"
          binary-state-sort
          flat
          bordered>
          <template v-slot:body="props">
            <q-tr :props="props" @click="openConsultDetail(props.row)" class="cursor-pointer">
              <q-td key="id" :props="props">
                {{ props.row.id }}
              </q-td>
              <q-td key="type" :props="props">
                <q-badge :color="getConsultTypeColor(props.row.type)" text-color="white">
                  {{ getConsultTypeName(props.row.type) }}
                </q-badge>
              </q-td>
              <q-td key="title" :props="props">
                {{ props.row.title }}
              </q-td>
              <q-td key="createTime" :props="props">
                {{ formatDateTime(props.row.createTime) }}
              </q-td>
              <q-td key="lastReplyTime" :props="props">
                {{ formatDateTime(props.row.lastReplyTime) }}
              </q-td>
              <q-td key="status" :props="props">
                <q-badge :color="getStatusColor(props.row.status)">
                  {{ getStatusName(props.row.status) }}
                </q-badge>
              </q-td>
              <q-td key="actions" :props="props" auto-width>
                <q-btn flat round dense icon="visibility" color="primary" @click.stop="openConsultDetail(props.row)" />
                <q-btn v-if="props.row.status !== 'closed'" flat round dense icon="close" color="negative" @click.stop="confirmCloseConsult(props.row)" />
              </q-td>
            </q-tr>
          </template>

          <template v-slot:no-data>
            <div class="full-width row flex-center q-py-lg">
              <q-icon name="question_answer" size="2em" color="grey-5" />
              <div class="q-ml-sm text-grey-7">{{ $t('messageCenter.consult.noConsults') }}</div>
            </div>
          </template>

          <template v-slot:pagination="scope">
            <div class="row items-center justify-end q-py-sm">
              <div class="col-auto">
                <q-pagination v-model="pagination.page" :max="Math.ceil(scope.pagesNumber)" :max-pages="6" :boundary-links="$q.screen.gt.xs" :direction-links="true" @update:model-value="onRequest" />
              </div>
            </div>
          </template>
        </q-table>
      </div>

      <!-- 移动端卡片视图 -->
      <div v-else class="mobile-view">
        <div v-if="consults.length === 0" class="text-center q-py-xl">
          <q-icon name="question_answer" size="3em" color="grey-5" />
          <div class="q-mt-sm text-grey-7">{{ $t('messageCenter.consult.noConsults') }}</div>
        </div>

        <q-list separator>
          <q-item v-for="consult in consults" :key="consult.id" clickable v-ripple @click="openConsultDetail(consult)">
            <q-item-section>
              <q-item-label class="row items-center">
                <q-badge :color="getConsultTypeColor(consult.type)" text-color="white" class="q-mr-sm">
                  {{ getConsultTypeName(consult.type) }}
                </q-badge>
                <span class="text-weight-medium">{{ consult.title }}</span>
              </q-item-label>
              <q-item-label caption class="q-mt-xs">
                <div class="row justify-between">
                  <span>{{ $t('messageCenter.consult.idColumn') }}: {{ consult.id }}</span>
                  <q-badge :color="getStatusColor(consult.status)">
                    {{ getStatusName(consult.status) }}
                  </q-badge>
                </div>
              </q-item-label>
              <q-item-label caption class="q-mt-xs">
                <div class="row justify-between">
                  <span>{{ $t('messageCenter.consult.createTimeColumn') }}: {{ formatDate(consult.createTime) }}</span>
                  <span>{{ $t('messageCenter.consult.lastReplyTimeColumn') }}: {{ formatDate(consult.lastReplyTime) }}</span>
                </div>
              </q-item-label>
            </q-item-section>

            <q-item-section side>
              <div class="row">
                <q-btn flat round dense icon="visibility" color="primary" @click.stop="openConsultDetail(consult)" />
                <q-btn v-if="consult.status !== 'closed'" flat round dense icon="close" color="negative" @click.stop="confirmCloseConsult(consult)" />
              </div>
            </q-item-section>
          </q-item>
        </q-list>

        <!-- 移动端分页 -->
        <div class="row justify-center q-py-md">
          <q-pagination v-model="pagination.page" :max="Math.ceil(totalPages)" :max-pages="5" :boundary-links="false" :direction-links="true" @update:model-value="onRequest" />
        </div>
      </div>
    </div>

    <!-- 新建咨询弹窗 -->
    <q-dialog v-model="newConsultDialog" persistent>
      <q-card style="min-width: 350px; max-width: 80vw">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ $t('messageCenter.consult.newConsultTitle') }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form @submit="submitNewConsult" ref="newConsultForm">
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-select
                  v-model="newConsult.type"
                  :options="consultTypeOptions"
                  :label="$t('messageCenter.consult.consultTypeRequired')"
                  outlined
                  dense
                  emit-value
                  map-options
                  :rules="[(val) => !!val || $t('messageCenter.consult.typeRule')]" />
              </div>

              <div class="col-12" v-if="newConsult.type === 'order' || newConsult.type === 'transfer'">
                <q-select
                  v-model="newConsult.relatedId"
                  :options="relatedOptions"
                  :label="newConsult.type === 'order' ? $t('messageCenter.consult.relatedOrder') : $t('messageCenter.consult.relatedTransfer')"
                  outlined
                  dense
                  emit-value
                  map-options
                  use-input
                  hide-selected
                  fill-input
                  @filter="filterRelated" />
              </div>

              <div class="col-12">
                <q-input
                  v-model="newConsult.title"
                  :label="$t('messageCenter.consult.consultTitleRequired')"
                  outlined
                  dense
                  :rules="[(val) => (!!val && val.length <= 50) || $t('messageCenter.consult.titleRule')]" />
              </div>

              <div class="col-12">
                <q-input
                  v-model="newConsult.content"
                  :label="$t('messageCenter.consult.consultContentRequired')"
                  type="textarea"
                  outlined
                  autogrow
                  :rules="[(val) => (!!val && val.length <= 500) || $t('messageCenter.consult.contentRule')]" />
                <div class="text-caption text-right text-grey-7">{{ newConsult.content.length }}/500</div>
              </div>

              <div class="col-12">
                <q-file
                  v-model="newConsult.attachments"
                  :label="$t('messageCenter.consult.attachments')"
                  outlined
                  dense
                  multiple
                  accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx"
                  max-files="3"
                  max-file-size="5242880"
                  @rejected="onRejected">
                  <template #prepend>
                    <q-icon name="attach_file" />
                  </template>
                  <template #hint> {{ $t('messageCenter.consult.attachmentsHint') }} </template>
                </q-file>
              </div>
            </div>

            <div class="row justify-end q-mt-md">
              <q-btn :label="$t('messageCenter.consult.cancel')" flat color="primary" v-close-popup />
              <q-btn :label="$t('messageCenter.consult.submit')" type="submit" color="primary" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 关闭咨询确认弹窗 -->
    <q-dialog v-model="closeConsultDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">{{ $t('messageCenter.consult.confirmClose') }}</span>
        </q-card-section>

        <q-card-section> {{ $t('messageCenter.consult.confirmCloseMessage') }} </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('messageCenter.consult.cancel')" color="primary" v-close-popup />
          <q-btn flat :label="$t('messageCenter.consult.confirm')" color="negative" @click="closeConsult" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar, date } from 'quasar';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
// import ConsultApi from '~/composables/consultApi'; // 实际项目中取消注释

const $q = useQuasar();
const router = useRouter();
const { t } = useI18n();

// 筛选条件
const filter = reactive({
  type: 'all',
  status: 'all',
  dateRange: {
    from: null,
    to: null,
  },
  keyword: '',
});

// 咨询类型选项
const consultTypeOptions = [
  { label: t('messageCenter.consult.allTypes'), value: 'all' },
  { label: t('messageCenter.consult.orderConsult'), value: 'order' },
  { label: t('messageCenter.consult.transferConsult'), value: 'transfer' },
  { label: t('messageCenter.consult.productConsult'), value: 'product' },
  { label: t('messageCenter.consult.aftersaleService'), value: 'aftersale' },
  { label: t('messageCenter.consult.complaint'), value: 'complaint' },
  { label: t('messageCenter.consult.otherIssue'), value: 'other' },
];

// 状态选项
const statusOptions = [
  { label: t('messageCenter.consult.allStatuses'), value: 'all' },
  { label: t('messageCenter.consult.pending'), value: 'pending' },
  { label: t('messageCenter.consult.replied'), value: 'replied' },
  { label: t('messageCenter.consult.closed'), value: 'closed' },
];

// 表格列定义
const columns = [
  { name: 'id', align: 'left', label: t('messageCenter.consult.idColumn'), field: 'id', sortable: true },
  { name: 'type', align: 'left', label: t('messageCenter.consult.typeColumn'), field: 'type', sortable: true },
  { name: 'title', align: 'left', label: t('messageCenter.consult.titleColumn'), field: 'title', sortable: true },
  { name: 'createTime', align: 'left', label: t('messageCenter.consult.createTimeColumn'), field: 'createTime', sortable: true, format: (val) => formatDateTime(val) },
  { name: 'lastReplyTime', align: 'left', label: t('messageCenter.consult.lastReplyTimeColumn'), field: 'lastReplyTime', sortable: true, format: (val) => formatDateTime(val) },
  { name: 'status', align: 'left', label: t('messageCenter.consult.statusColumn'), field: 'status', sortable: true },
  { name: 'actions', align: 'center', label: t('messageCenter.consult.actionsColumn') },
];

// 分页设置
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  sortBy: 'createTime',
  descending: true,
});

// 咨询列表
const consults = ref([]);
const totalPages = ref(1);
const loading = ref(false);

// 新建咨询弹窗
const newConsultDialog = ref(false);
const newConsultForm = ref(null);
const newConsult = reactive({
  type: '',
  relatedId: null,
  title: '',
  content: '',
  attachments: [],
});

// 关闭咨询确认弹窗
const closeConsultDialog = ref(false);
const consultToClose = ref(null);

// 相关订单/转运单选项
const relatedOptions = ref([]);

// 加载咨询列表
const loadConsults = async () => {
  loading.value = true;
  try {
    // 实际项目中使用API调用
    // const response = await ConsultApi.getConsults({
    //   page: pagination.value.page,
    //   pageSize: pagination.value.rowsPerPage,
    //   type: filter.type,
    //   status: filter.status,
    //   startDate: filter.dateRange.from,
    //   endDate: filter.dateRange.to,
    //   keyword: filter.keyword,
    //   sortBy: pagination.value.sortBy,
    //   descending: pagination.value.descending
    // });

    // 模拟数据
    await new Promise((resolve) => setTimeout(resolve, 500));

    const mockConsults = generateMockConsults();
    consults.value = mockConsults.data;
    totalPages.value = Math.ceil(mockConsults.total / pagination.value.rowsPerPage);
  } catch (error) {
    console.error('加载咨询失败', error);
    $q.notify({
      color: 'negative',
      message: t('messageCenter.consult.loadFailed'),
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 表格请求处理
const onRequest = (props) => {
  if (props.pagination) {
    pagination.value = props.pagination;
  }
  loadConsults();
};

// 打开咨询详情
const openConsultDetail = (consult) => {
  router.push(`/account/consult-detail/${consult.id}`);
};

// 打开新建咨询弹窗
const openNewConsultDialog = () => {
  // 重置表单
  Object.assign(newConsult, {
    type: '',
    relatedId: null,
    title: '',
    content: '',
    attachments: [],
  });

  newConsultDialog.value = true;
};

// 提交新建咨询
const submitNewConsult = async () => {
  try {
    // 实际项目中使用API调用
    // const response = await ConsultApi.createConsult(newConsult);

    // 模拟提交
    await new Promise((resolve) => setTimeout(resolve, 500));

    $q.notify({
      color: 'positive',
      message: t('messageCenter.consult.submitSuccess'),
      icon: 'check',
    });

    newConsultDialog.value = false;
    loadConsults();
  } catch (error) {
    console.error('提交咨询失败', error);
    $q.notify({
      color: 'negative',
      message: t('messageCenter.consult.submitFailed'),
      icon: 'error',
    });
  }
};

// 确认关闭咨询
const confirmCloseConsult = (consult) => {
  consultToClose.value = consult;
  closeConsultDialog.value = true;
};

// 关闭咨询
const closeConsult = async () => {
  try {
    // 实际项目中使用API调用
    // await ConsultApi.closeConsult(consultToClose.value.id);

    // 模拟关闭
    const index = consults.value.findIndex((c) => c.id === consultToClose.value.id);
    if (index !== -1) {
      consults.value[index].status = 'closed';
    }

    $q.notify({
      color: 'positive',
      message: t('messageCenter.consult.closeSuccess'),
      icon: 'check',
    });
  } catch (error) {
    console.error('关闭咨询失败', error);
    $q.notify({
      color: 'negative',
      message: t('messageCenter.consult.closeFailed'),
      icon: 'error',
    });
  }
};

// 过滤相关订单/转运单
const filterRelated = (val, update) => {
  if (val === '') {
    update(() => {
      relatedOptions.value = [];
    });
    return;
  }

  update(() => {
    // 实际项目中使用API调用
    // const response = await ConsultApi.searchRelated(newConsult.type, val);
    // relatedOptions.value = response.data;

    // 模拟数据
    const type = newConsult.type;
    const prefix = type === 'order' ? 'ORD' : 'TRF';

    relatedOptions.value = [
      { label: `${prefix}10001 - 2023-05-01`, value: '10001' },
      { label: `${prefix}10002 - 2023-05-05`, value: '10002' },
      { label: `${prefix}10003 - 2023-05-10`, value: '10003' },
      { label: `${prefix}${val} - 模拟数据`, value: val },
    ];
  });
};

// 文件上传被拒绝处理
const onRejected = (rejectedEntries) => {
  $q.notify({
    type: 'negative',
    message: rejectedEntries.length > 1 ? t('messageCenter.consult.filesRejected', { count: rejectedEntries.length }) : t('messageCenter.consult.fileRejected'),
  });
};

// 获取咨询类型颜色
const getConsultTypeColor = (type) => {
  const colors = {
    order: 'primary',
    transfer: 'purple',
    product: 'teal',
    aftersale: 'orange',
    complaint: 'red',
    other: 'blue-grey',
  };
  return colors[type] || 'grey';
};

// 获取咨询类型名称
const getConsultTypeName = (type) => {
  const names = {
    order: t('messageCenter.consult.orderConsult'),
    transfer: t('messageCenter.consult.transferConsult'),
    product: t('messageCenter.consult.productConsult'),
    aftersale: t('messageCenter.consult.aftersaleService'),
    complaint: t('messageCenter.consult.complaint'),
    other: t('messageCenter.consult.otherIssue'),
  };
  return names[type] || type;
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    pending: 'orange',
    replied: 'green',
    closed: 'grey',
  };
  return colors[status] || 'grey';
};

// 获取状态名称
const getStatusName = (status) => {
  const names = {
    pending: t('messageCenter.consult.pending'),
    replied: t('messageCenter.consult.replied'),
    closed: t('messageCenter.consult.closed'),
  };
  return names[status] || status;
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  const dateObj = new Date(timestamp);
  return date.formatDate(dateObj, 'YYYY-MM-DD HH:mm');
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const dateObj = new Date(timestamp);
  return date.formatDate(dateObj, 'YYYY-MM-DD');
};

// 生成模拟咨询数据
const generateMockConsults = () => {
  const total = 25;
  const pageSize = pagination.value.rowsPerPage;
  const currentPage = pagination.value.page;

  const allConsults = [];
  const types = ['order', 'transfer', 'product', 'aftersale', 'complaint', 'other'];
  const statuses = ['pending', 'replied', 'closed'];
  const now = new Date();

  for (let i = 0; i < total; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const createTime = new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);
    const lastReplyTime = status === 'pending' ? null : new Date(createTime.getTime() + Math.floor(Math.random() * 3) * 24 * 60 * 60 * 1000);

    let title;
    switch (type) {
      case 'order':
        title = `关于订单 #ORD${10001 + i} 的发货问题`;
        break;
      case 'transfer':
        title = `转运单 #TRF${20001 + i} 的物流查询`;
        break;
      case 'product':
        title = `商品 #PRD${30001 + i} 的规格咨询`;
        break;
      case 'aftersale':
        title = `订单 #ORD${10001 + i} 的退款申请`;
        break;
      case 'complaint':
        title = `关于物流速度的投诉`;
        break;
      case 'other':
        title = `其他问题咨询 #${i + 1}`;
        break;
    }

    allConsults.push({
      id: `CSL${100000 + i}`,
      type,
      title,
      createTime: createTime.getTime(),
      lastReplyTime: lastReplyTime ? lastReplyTime.getTime() : null,
      status,
    });
  }

  // 排序
  if (pagination.value.sortBy) {
    allConsults.sort((a, b) => {
      let aValue = a[pagination.value.sortBy];
      let bValue = b[pagination.value.sortBy];

      // 处理可能为null的lastReplyTime
      if (pagination.value.sortBy === 'lastReplyTime') {
        aValue = aValue || 0;
        bValue = bValue || 0;
      }

      if (pagination.value.descending) {
        return aValue > bValue ? -1 : 1;
      } else {
        return aValue < bValue ? -1 : 1;
      }
    });
  }

  // 筛选
  let filteredConsults = [...allConsults];

  if (filter.type !== 'all') {
    filteredConsults = filteredConsults.filter((consult) => consult.type === filter.type);
  }

  if (filter.status !== 'all') {
    filteredConsults = filteredConsults.filter((consult) => consult.status === filter.status);
  }

  if (filter.keyword) {
    const keyword = filter.keyword.toLowerCase();
    filteredConsults = filteredConsults.filter((consult) => consult.id.toLowerCase().includes(keyword) || consult.title.toLowerCase().includes(keyword));
  }

  if (filter.dateRange.from && filter.dateRange.to) {
    const fromDate = new Date(filter.dateRange.from).getTime();
    const toDate = new Date(filter.dateRange.to).getTime() + 24 * 60 * 60 * 1000 - 1; // 包含结束日期的全天

    filteredConsults = filteredConsults.filter((consult) => consult.createTime >= fromDate && consult.createTime <= toDate);
  }

  // 分页
  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  const paginatedConsults = filteredConsults.slice(start, end);

  return {
    data: paginatedConsults,
    total: filteredConsults.length,
  };
};

// 页面加载时获取咨询列表
onMounted(() => {
  loadConsults();
});
</script>

<style lang="scss" scoped>
.message-consult {
  .filter-section {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  }

  .consult-filter {
    min-width: 150px;
  }

  .consult-search {
    min-width: 200px;
  }

  .consult-list {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  }

  // 移动端样式
  @media (max-width: 599px) {
    .filter-section {
      .q-select,
      .q-input {
        margin-bottom: 8px;
      }
    }

    .mobile-view {
      .q-item {
        padding: 12px;
      }
    }
  }
}
</style>
