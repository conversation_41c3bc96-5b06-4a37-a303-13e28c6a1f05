export const BannerTag = {
  TOPAD: 'topAd', //顶部广告
  EVENT: 'event', //活动
  BLOG: 'blog', //活动
};

/**
 * 促销展位位置
 */
export const PromotionPosition = {
  RECOMMEND: { positionId: 1, title: 'Recommend' }, //推荐
  NEW: { positionId: 2, title: 'New' }, //新品
  HOT: { positionId: 3, title: 'Hot' }, //最佳
  ONSALE: { positionId: 4, title: 'Onsale' }, //特价
  SIDEBAR_NEW: { positionId: 5, title: 'New' }, //侧边新品
  SIDEBAR_HOT: { positionId: 6, title: 'Hot' }, //侧边最佳
  GRID_6: { positionId: 7, title: 'Grid6' }, //六宫格
};

/**
 * 文案类型
 */
export const ArticleCategory = {
  BLOG: 4, //博客
};

export const SortField = {
  PRICE: 'price', //价格
  SALES_COUNT: 'salesCount', //销量 hot
  CREATE_TIME: 'createTime', //新品
  ALPHABETIC: 'alphabetic', //字典排序
  SCORE: 'score', //评分
};

export const PayOrderSources = {
  PARCEL: 'p', //包裹订单
  ORDER: 'o', //购物订单
  CHARGE: 'c', //充值订单
  SUPPLEMENT: 's', //充值订单
};

/**
 * 售后状态枚举
 */
export const AfterSaleStatusEnum = {
  APPLY: 10, // 申请中
  SELLER_AGREE: 20, // 卖家通过
  BUYER_DELIVERY: 30, // 待卖家收货
  WAIT_REFUND: 40, // 等待平台退款
  COMPLETE: 50, // 完成
  BUYER_CANCEL: 61, // 买家取消售后
  SELLER_DISAGREE: 62, // 卖家拒绝
  SELLER_REFUSE: 63, // 卖家拒绝收货
};

/**
 * 售后方式枚举
 */
export const AfterSaleWayEnum = {
  REFUND: 10, // 仅退款
  RETURN_AND_REFUND: 20, // 退货退款
};

/**
 * 售后类型枚举
 */
export const AfterSaleTypeEnum = {
  IN_SALE: 10, // 售中退款
  AFTER_SALE: 20, // 售后退款
};
