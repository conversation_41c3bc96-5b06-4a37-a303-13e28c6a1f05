import { $fetch } from 'ofetch';
import { useRuntimeConfig } from '#app';
import { useAuthStore } from '~/store/auth';
import { useCartStore } from '~/store/cart';
import { useWishlistStore } from '~/store/wishlist';
import { showNotify } from '../utils/utils';

 
interface RequestOptions {
  custom?: {
    showSuccess?: false; // 是否显示成功提示
    showError?: false;   // 是否显示失败提示
    successMsg?: string;   // 自定义成功提示内容
    errorMsg?: string;     // 自定义失败提示内容
    auth?: false;        // 是否需要鉴权
  };
  headers?: Record<string, string>;
  [key: string]: any;
}
 
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

// 自定义方法：显示全局提示
const showToast = ({ message, type = 'info', }: { message: string; type?: string;}) => {
  showNotify(message,type);
};

const useLanguage = () => {
  if (process.server) {
    const cookies = parseCookies(useRequestEvent());
    return cookies.language || 'en'; // 默认语言为 'en'
  } else if (process.client) {
    return localStorage.getItem('language') || 'en'; // 默认语言为 'en'    // 客户端从 localStorage 获取语言
  }
};

 
// 请求拦截器
function handleRequest(options: RequestOptions) {
  const selectedLanguage = useLanguage() || 'en'; 
  const token = useAuthStore().useToken();

  options.headers = {
    ...options.headers,
    'Content-Type': 'application/json',
    ...(token ? { Authorization: `Bearer ${token}` } : {}), // 动态添加 Authorization   
    'tenant-id': globalConfig.tenantId, // 添加租户 ID
    lang: selectedLanguage,                              // 动态语言
  };

  // 如果需要鉴权但用户未登录，显示提示并中断请求
  if (options.custom?.auth && !useAuthStore().isLogin) {
    showToast({ message: '请先登录后再操作', type: 'warning' });
    throw new Error('用户未登录，已拒绝请求。');
  }
}

// 响应拦截器
function handleResponse(response: any, customOptions: RequestOptions['custom'], url: string, options: RequestOptions) {
  const resCode = response.code ?? {}; // 解析返回数据
  const resData = response.data ?? {}; // 解析返回数据
  const resMsg = response.msg ?? {};

  // 自动处理登录相关接口的 Token
  if (url.includes('/member/auth/') && resData.accessToken) {
    useAuthStore().saveToken(resData.accessToken, resData.refreshToken)
  }
  if (resCode !== 0) {
    if (resCode === 401) {
      return refreshToken(url, options); // 刷新 Token 并重试请求
    }

    if (customOptions?.showError) {
      let errorMessage = '网络请求出错';
      if(customOptions?.errorMsg || resMsg){
        errorMessage = customOptions.errorMsg || resMsg;
      }else{
        switch (resCode) {
          case 400:
            errorMessage = '请求错误';
            break;
          case 403:
            errorMessage = '拒绝访问';
            break;
          case 404:
            errorMessage = '请求出错';
            break;
          case 408:
            errorMessage = '请求超时';
            break;
          case 429:
            errorMessage = '请求频繁, 请稍后再访问';
            break;
          case 500:
            errorMessage = '服务器开小差啦,请稍后再试~';
            break;
          case 501:
            errorMessage = '服务未实现';
            break;
          case 502:
            errorMessage = '网络错误';
            break;
          case 503:
            errorMessage = '服务不可用';
            break;
          case 504:
            errorMessage = '网络超时';
            break;
          case 505:
            errorMessage = 'HTTP 版本不受支持';
            break;
        }
        if (resMsg.includes('timeout')) errorMessage = '请求超时';
        if (resMsg.includes('Network')) errorMessage = '服务器异常';
      }
      showToast({ message: errorMessage , type: 'error' });
    }


    // throw new Error(resMsg || '响应错误');
  }else{
    if (customOptions?.showSuccess) {
      showToast({ message: customOptions?.successMsg || '操作成功！', type: 'success' });
    }
  }
  return response;
}
 
/**
 * 创建请求方法
 * @param method
 */
function createFetchRequest(method: HttpMethod) {
  return async function (
    url: string,
    options: RequestOptions = {}
  ) {
    const baseURL = useRuntimeConfig().public.baseUrl + useRuntimeConfig().public.apiPath;
    const requestUrl = `${baseURL.replace(/\/$/, '')}/${url.replace(/^\//, '')}`;

    try {
      handleRequest(options);
      // 解构 custom 和 body，其余作为合法请求参数传递
      const { custom, body, ...fetchOptions } = options;
      const response = await $fetch(requestUrl, {
        method,
        body,
        ...fetchOptions,
        onResponse({ request, response }) {
          // console.log('HTTP 状态码:', response.status); // 成功时获取 HTTP 状态码
          // if(response.ok){
          //   console.log('响应数据:', response._data);
          // }
        },
        onResponseError({ request, response }) {
          if (response) {
            const { status } = response;
            const { message, action } = getErrorHandler(status);
             // 显示错误消息
            showToast({ message, type: 'error' });
            // 执行错误码对应的逻辑
            action();
          } else {
            console.error('请求未完成，可能是网络错误');
            showToast({ message: '网络请求错误，请稍后重试！', type: 'error' });
          }
        },
      });
      return handleResponse(response, custom, requestUrl, { ...options, method });
    } catch (error) {
      // if (options.custom?.showError) {
      //   showToast({ message: options.custom?.errorMsg || '网络请求错误，请稍后重试！', type: 'error' });
      // }
      // throw error;
    }
  };
}

async function refreshToken(originalUrl: string, originalOptions: RequestOptions) {
  const authStore = useAuthStore();
  const tenantId = globalConfig.tenantId;
  const refreshToken =  authStore.useRefreshToken();

  if (!refreshToken) {
    authStore.clearToken();
    useRouter().push('/login');
    throw new Error('刷新 Token 失败：用户未登录');
  }

  try {
    const { public: { baseUrl,apiPath } } = useRuntimeConfig();
    const refreshResponse: any = await $fetch(`${baseUrl}${apiPath}/member/auth/refresh-token`, {
      method: 'POST',
      params: { refreshToken },
      headers: {
        'Content-Type': 'application/json',
        'tenant-id': tenantId, // 添加租户 ID
      },
    });

    if (refreshResponse.code === 0 && refreshResponse.data?.accessToken) {
      authStore.saveToken(refreshResponse.data.accessToken, refreshResponse.data.refreshToken)

      originalOptions.headers = {
        ...originalOptions.headers,
        Authorization: `Bearer ${refreshResponse.data.accessToken}`,
      };
      // 使用原始请求方法重试请求
      return await $fetch(originalUrl, {
        ...originalOptions,
        method: originalOptions.method || 'GET', // 确保请求方法正确
      });
    } else {
      //刷新失败，清除token
      clearCache();
      return refreshResponse;
      // throw new Error('刷新 Token 失败');
    }
  } catch (error) {
    console.error('刷新 Token 失败：---------------------------------', error);
    clearCache();
  }
}

function clearCache() {
  useAuthStore().clearToken();
  const tokenCookie = useCookie('token');
  const refreshTokenCookie = useCookie('refreshToken');
  tokenCookie.value = null;
  refreshTokenCookie.value = null;

  //清除购物车和心愿单
  useCartStore().clearCart();
  useWishlistStore().clearWishlist();
}

// 自定义方法：获取错误消息和处理逻辑
const getErrorHandler = (statusCode: number) => {
  const errorHandlers = {
    400: {
      message: '请求错误',
      action: () => console.warn('请检查请求参数是否正确'),
    },
    401: {
      message: '您的登录已过期，请重新登录',
      action: () => {
        console.warn('跳转到登录页面');
        useRouter().push('/login'); // 跳转到登录页面
      },
    },
    403: {
      message: '拒绝访问',
      action: () => console.warn('您没有权限访问该资源'),
    },
    404: {
      message: '请求出错',
      action: () => console.warn('未找到请求的资源'),
    },
    408: {
      message: '请求超时',
      action: () => console.warn('请求超时，请重试'),
    },
    429: {
      message: '请求频繁，请稍后再访问',
      action: () => console.warn('请稍后再试'),
    },
    500: {
      message: '服务器开小差啦，请稍后再试~',
      action: () => console.warn('服务器内部错误'),
    },
    501: {
      message: '服务未实现',
      action: () => console.warn('服务未实现'),
    },
    502: {
      message: '网络错误',
      action: () => console.warn('网关错误'),
    },
    503: {
      message: '服务不可用',
      action: () => console.warn('服务不可用'),
    },
    504: {
      message: '网络超时',
      action: () => console.warn('网关超时'),
    },
    505: {
      message: 'HTTP 版本不受支持',
      action: () => console.warn('服务器不支持请求的 HTTP 版本'),
    },
  };

  // 返回对应的处理逻辑或默认值
  return errorHandlers[statusCode] || {
    message: '未知错误',
    action: () => console.warn('未知错误，请联系管理员'),
  };
};


 
// 提供 $fetch & HTTP 方法 - 统一管理请求 - 再到组件中使用
export const useClientGet = createFetchRequest('GET');
export const useClientPost = createFetchRequest('POST');
export const useClientPut = createFetchRequest('PUT');
export const useClientDelete = createFetchRequest('DELETE');